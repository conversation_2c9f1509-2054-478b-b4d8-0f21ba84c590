export interface Token {
  id: string
  symbol: string
  name: string
  logoUrl?: string
  decimals: number
  mintAddress: string
}

export interface StakingPool {
  id: string
  tokenId: string
  name: string
  apy: number
  totalStaked: number
  maxStake?: number
  minStake: number
  isActive: boolean
  token?: Token
}

export interface StakingPosition {
  id: string
  userId: string
  tokenId: string
  poolId: string
  amount: number
  stakedAt: Date
  lastRewardAt: Date
  isActive: boolean
  token?: Token
  pool?: StakingPool
}

export interface Protocol {
  id: string
  name: string
  description: string
  logoUrl?: string
  category: string
  tvl?: number
  apy?: number
  isActive: boolean
}

export interface User {
  id: string
  walletAddress: string
  createdAt: Date
  updatedAt: Date
  stakingPositions?: StakingPosition[]
}

export interface HeroStats {
  totalStaking: number
  freeEquity: number
  totalRewards: number
  activePositions: number
}

export interface TokenStakingInfo {
  token: Token
  pool: StakingPool
  userPosition?: StakingPosition
  pendingRewards: number
  flexibleSavings: number
  lockedSavings: number
  flexibleApy: number
  lockedApy: number
  duration: string
}
