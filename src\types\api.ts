// API Request/Response Types for Solera Staking Application

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  timestamp: string
}

export interface ApiError {
  code: string
  message: string
  details?: any
  timestamp: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// User Profile Types
export interface UserProfile {
  id: string
  walletAddress: string
  username?: string
  email?: string
  avatar?: string
  createdAt: string
  updatedAt: string
  preferences: UserPreferences
  stats: UserStats
}

export interface UserPreferences {
  theme: 'dark' | 'light'
  notifications: boolean
  language: string
  currency: string
}

export interface UserStats {
  totalStaked: number
  totalRewards: number
  stakingPositions: number
  joinDate: string
  lastActivity: string
}

// Wallet Connection Types
export interface WalletConnectionEvent {
  walletAddress: string
  walletType: string
  network: string
  timestamp: string
  userAgent?: string
  ipAddress?: string
}

export interface WalletConnectionResponse extends ApiResponse {
  data: {
    sessionId: string
    user: UserProfile
    isNewUser: boolean
  }
}

// Analytics Types
export interface AnalyticsEvent {
  event: string
  properties: Record<string, any>
  userId?: string
  sessionId?: string
  timestamp: string
}

export interface AnalyticsResponse extends ApiResponse {
  data: {
    eventId: string
    processed: boolean
  }
}

// Token Balance Types
export interface TokenBalanceRequest {
  walletAddress: string
  tokenAddress: string
  network: string
}

export interface TokenBalanceResponse extends ApiResponse {
  data: {
    balance: number
    decimals: number
    symbol: string
    name: string
    lastUpdated: string
  }
}

// Staking Types
export interface StakingPosition {
  id: string
  userId: string
  tokenAddress: string
  amount: number
  rewards: number
  startDate: string
  endDate?: string
  status: 'active' | 'completed' | 'cancelled'
  apy: number
}

export interface StakingPositionsResponse extends PaginatedResponse<StakingPosition> {}

// API Configuration Types
export interface ApiConfig {
  baseURL: string
  timeout: number
  retryAttempts: number
  headers?: Record<string, string>
}

export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  url: string
  data?: any
  params?: Record<string, any>
  headers?: Record<string, string>
  timeout?: number
  retryAttempts?: number
}

// Error Types
export interface NetworkError extends Error {
  code: 'NETWORK_ERROR'
  status?: number
  response?: any
}

export interface TimeoutError extends Error {
  code: 'TIMEOUT_ERROR'
  timeout: number
}

export interface ValidationError extends Error {
  code: 'VALIDATION_ERROR'
  fields: Record<string, string[]>
}

export interface AuthenticationError extends Error {
  code: 'AUTHENTICATION_ERROR'
  walletAddress: string | null
}

export interface RateLimitError extends Error {
  code: 'RATE_LIMIT_ERROR'
  retryAfter: number
}

// Union type for all possible API errors
export type ApiErrorType =
  | NetworkError
  | TimeoutError
  | ValidationError
  | AuthenticationError
  | RateLimitError
