'use client'

import { useState } from 'react'
import { formatCurrency } from '@/lib/utils'
import { ChevronDown, ArrowDownToLine, ArrowUpFromLine } from 'lucide-react'
import Image from 'next/image'
import { BuyRAModal } from '@/components/modals/BuyRAModal'
import './hero-animations.css'

export function Hero() {
  // Modal state
  const [isBuyModalOpen, setIsBuyModalOpen] = useState(false)

  // Mock data - in real app, this would come from API/database
  const stats = {
    totalStaking: 40899.12,
    freeEquity: 2390.44,
  }

  return (
    <div className="text-center py-12 relative overflow-hidden rounded-2xl p-4">
      {/* Dynamic Background Flare Effects */}
      <div className="absolute inset-0 pointer-events-none hero-flare-container">
        {/* Primary Glow Orb - Top Left */}
        <div className="absolute -top-20 -left-20 w-96 h-96 hero-glow-primary bg-gradient-radial from-blue-500/20 via-cyan-400/10 to-transparent rounded-full blur-3xl animate-pulse-slow opacity-60 hero-flare-element"></div>

        {/* Secondary Glow Orb - Bottom Right */}
        <div className="absolute -bottom-32 -right-32 w-80 h-80 hero-glow-secondary bg-gradient-radial from-teal-400/15 via-blue-600/8 to-transparent rounded-full blur-2xl animate-float opacity-50 hero-flare-element"></div>

        {/* Accent Glow - Center */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 hero-glow-accent bg-gradient-radial from-cyan-300/10 via-blue-400/5 to-transparent rounded-full blur-xl animate-breathe opacity-40 hero-flare-element"></div>

        {/* Light Rays */}
        <div className="absolute top-0 left-1/4 w-1 h-full hero-light-ray bg-gradient-to-b from-transparent via-cyan-400/20 to-transparent transform rotate-12 animate-shimmer opacity-30"></div>
        <div className="absolute top-0 right-1/3 w-1 h-full hero-light-ray bg-gradient-to-b from-transparent via-blue-400/15 to-transparent transform -rotate-12 animate-shimmer-delayed opacity-25"></div>

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      </div>

      {/* Action Buttons - Top Right */}
      <div className="absolute top-5 right-5 flex items-center space-x-2 z-10">
        <button
          className="w-9 h-9 rounded-sm bg-gray-800/50 border border-gray-700 flex items-center justify-center hover:bg-gray-700/50 transition-colors group"
          title="Deposit"
        >
          <ArrowDownToLine className="w-3 h-3 text-gray-400 group-hover:text-white transition-colors" />
        </button>
        <button
          className="w-9 h-9 rounded-sm bg-gray-800/50 border border-gray-700 flex items-center justify-center hover:bg-gray-700/50 transition-colors group"
          title="Withdraw"
        >
          <ArrowUpFromLine className="w-3 h-3 text-gray-400 group-hover:text-white transition-colors" />
        </button>
      </div>

      {/* Centered Stats */}
      <div className="flex items-center justify-center space-x-16 mb-8 pt-8 relative z-10">
        {/* Total in Staking */}
        <div className="text-center rounded-lg transition-all duration-300">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <span className="text-sm text-gray-400">Total in Staking</span>
            <ChevronDown className="w-4 h-4 text-gray-400" />
          </div>
          <div className="text-4xl font-bold text-white mb-1">
            {formatCurrency(stats.totalStaking)}
          </div>
          <div className="text-sm text-gray-500">0.0000098 BTC</div>
        </div>

        {/* Free Equity */}
        <div className="text-center rounded-lg transition-all duration-300">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <span className="text-sm text-gray-400">Free Equity</span>
            <ChevronDown className="w-4 h-4 text-gray-400" />
          </div>
          <div className="text-4xl font-bold text-white mb-1">
            {formatCurrency(stats.freeEquity)}
          </div>
          <div className="text-sm text-gray-500">0.0000098 BTC</div>
        </div>
      </div>

      {/* Integrated Wallet Banner */}
      <div className="flex items-center justify-center relative z-10">
        <div className="bg-gradient-to-r from-cyan-400 via-blue-500 to-teal-500 rounded-full px-3 py-2 flex items-center space-x-5 shadow-xl backdrop-blur-sm hero-banner-enhanced">
          {/* Platform Logos - Overlapping */}
          <div className="flex items-center">
            {/* DEXTools Logo */}
            <div className="w-10 h-10 rounded-full bg-white/15 backdrop-blur-md flex items-center justify-center border border-white/30 hover:bg-white/25 transition-all duration-300 shadow-lg relative z-40 hover:z-50">
              <div className="w-7 h-7 rounded-full overflow-hidden flex items-center justify-center bg-white/90">
                <Image
                  src="/buy/dextool.png"
                  alt="DEXTools"
                  width={28}
                  height={28}
                  className="w-full h-full object-contain p-0.5"
                />
              </div>
            </div>

            {/* DEXScreener Logo */}
            <div className="w-10 h-10 rounded-full bg-white/15 backdrop-blur-md flex items-center justify-center border border-white/30 hover:bg-white/25 transition-all duration-300 shadow-lg relative z-30 hover:z-50 -ml-5">
              <div className="w-7 h-7 rounded-full overflow-hidden flex items-center justify-center bg-white/90">
                <Image
                  src="/buy/dexscreener.png"
                  alt="DEXScreener"
                  width={28}
                  height={28}
                  className="w-full h-full object-contain p-0.5"
                />
              </div>
            </div>

            {/* Meteora Logo */}
            <div className="w-10 h-10 rounded-full bg-white/15 backdrop-blur-md flex items-center justify-center border border-white/30 hover:bg-white/25 transition-all duration-300 shadow-lg relative z-20 hover:z-50 -ml-5">
              <div className="w-7 h-7 rounded-full overflow-hidden flex items-center justify-center bg-white/90">
                <Image
                  src="/buy/meteora.png"
                  alt="Meteora"
                  width={28}
                  height={28}
                  className="w-full h-full object-contain p-0.5"
                />
              </div>
            </div>

            {/* Raydium Logo */}
            <div className="w-10 h-10 rounded-full bg-white/15 backdrop-blur-md flex items-center justify-center border border-white/30 hover:bg-white/25 transition-all duration-300 shadow-lg relative z-10 hover:z-50 -ml-5">
              <div className="w-7 h-7 rounded-full overflow-hidden flex items-center justify-center bg-white/90">
                <Image
                  src="/buy/raydium.png"
                  alt="Raydium"
                  width={28}
                  height={28}
                  className="w-full h-full object-contain p-0.5"
                />
              </div>
            </div>
          </div>

          <div className="text-white text-left">
            <span className="text-base font-medium tracking-normal text-ellipsis overflow-hidden">Manage your stakings assets at one place</span>
          </div>

          <button
            onClick={() => setIsBuyModalOpen(true)}
            className="bg-white text-black rounded-full text-sm font-semibold px-3 py-1 transition-all duration-300 shadow-lg border-0 min-h-0"
          >
            Buy
          </button>
        </div>
      </div>

      {/* Buy RA Modal */}
      <BuyRAModal
        isOpen={isBuyModalOpen}
        onClose={() => setIsBuyModalOpen(false)}
      />
    </div>
  )
}
