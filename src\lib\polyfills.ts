// Polyfills for Solana and crypto operations in browser environment
// This file helps resolve bigint binding warnings

// Buffer polyfill for browser
import { <PERSON><PERSON><PERSON> } from 'buffer'

// Make Buffer available globally
if (typeof window !== 'undefined') {
  window.Buffer = Buffer
  
  // Polyfill for global process
  if (!window.process) {
    window.process = {
      env: {},
      nextTick: (fn: Function) => setTimeout(fn, 0),
      version: '',
      platform: 'browser',
    } as any
  }

  // Polyfill for global global
  if (!window.global) {
    window.global = window
  }
}

// Export for server-side usage
export { Buffer }

// Additional crypto polyfills
export const polyfills = {
  buffer: Buffer,
  process: typeof process !== 'undefined' ? process : {
    env: {},
    nextTick: (fn: Function) => setTimeout(fn, 0),
    version: '',
    platform: 'browser',
  },
}
