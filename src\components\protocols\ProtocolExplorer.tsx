'use client'

import { ChevronRight } from 'lucide-react'

export function ProtocolExplorer() {
  // Mock protocol data matching the image
  const protocols = [
    {
      id: '1',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      description: 'Swellchain is here! The restaking chain, powered by Proof of Restake.',
      logoUrl: '/logos/swellchain.png',
      category: 'Restaking',
      color: 'from-blue-500 to-cyan-600'
    },
    {
      id: '2',
      name: 'LINEAR Protocol',
      description: 'Highest Yield, Instant Liquidity. Your Journey of DeFi on NEAR starts here.',
      logoUrl: '/logos/linear.png',
      category: 'Liquid Staking',
      color: 'from-teal-500 to-cyan-600'
    },
    {
      id: '3',
      name: 'frax.finance',
      description: 'Inventors of the fractional stablecoin & 1st crypto native DeFi (Frax Price Index)',
      logoUrl: '/logos/frax.png',
      category: 'DeFi',
      color: 'from-gray-600 to-gray-800'
    },
    {
      id: '4',
      name: 'Eigenlayer',
      description: 'Deploy your own AVS across the Swell ecosystem',
      logoUrl: '/logos/eigenlayer.png',
      category: 'Restaking',
      color: 'from-indigo-500 to-blue-600'
    }
  ]

  return (
    <div className="mb-8 space-y-2 p-6 rounded-2xl" style={{ background: 'var(--bg-primary)' }}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white">Explore latest dApps</h2>
        <ChevronRight className="w-5 h-5 text-gray-400" />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {protocols.map((protocol) => (
          <div
            key={protocol.id}
            className="group cursor-pointer"
          >
            <div className="pt-4 pb-4">
              <div className="flex items-start space-x-3">
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${protocol.color} flex items-center justify-center`}>
                  <span className="text-white font-bold text-sm">
                    {protocol.name.charAt(0)}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-white text-sm mb-1">
                    {protocol.name}
                  </h3>
                  <p className="text-xs text-gray-400 line-clamp-3">
                    {protocol.description}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
