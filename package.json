{"name": "solera-staking", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:clean": "node scripts/build-clean.js", "build:vercel": "node scripts/vercel-build.js", "build:verbose": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate || true"}, "dependencies": {"@prisma/client": "^5.7.1", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.35", "@solana/wallet-adapter-wallets": "^0.19.26", "@solana/web3.js": "^1.87.6", "assert": "^2.1.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "clsx": "^2.0.0", "crypto-browserify": "^3.12.1", "https-browserify": "^1.0.0", "lucide-react": "^0.294.0", "next": "14.0.4", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "react": "^18", "react-dom": "^18", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tailwind-merge": "^2.2.0", "url": "^0.11.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "node-loader": "^2.1.0", "pino-pretty": "^13.0.0", "postcss": "^8", "prisma": "^5.7.1", "tailwindcss": "^3.3.0", "typescript": "^5"}}