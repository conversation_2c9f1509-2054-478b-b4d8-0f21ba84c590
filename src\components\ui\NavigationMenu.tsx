'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
const navigation = [
  { name: 'Dashboard', href: '/' },
  { name: 'Assets', href: '/assets' },
  { name: 'Planning', href: '/planning' },
  { name: 'Docs', href: 'https://docs.solera.work', external: true },
]

export function NavigationMenu() {
  const pathname = usePathname()

  return (
    /* Desktop Navigation */
    <nav className="hidden md:flex items-center space-x-6">
      {navigation.map((item) => {
        const isActive = pathname === item.href && !item.external

        if (item.external) {
          return (
            <a
              key={item.name}
              href={item.href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs font-medium text-gray-400 hover:text-gray-300 transition-colors"
            >
              {item.name}
            </a>
          )
        }

        return (
          <Link
            key={item.name}
            href={item.href}
            className={`text-xs font-medium transition-colors relative ${
              isActive
                ? 'text-white'
                : 'text-gray-400 hover:text-gray-300'
            }`}
          >
            {item.name}
            {isActive && (
              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white"></div>
            )}
          </Link>
        )
      })}
    </nav>
  )
}
