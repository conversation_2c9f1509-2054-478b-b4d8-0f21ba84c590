'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useEffect, useState, useRef } from 'react'
import { Menu, X } from 'lucide-react'

const navigation = [
  { name: 'Dashboard', href: '/' },
  { name: 'Assets', href: '/assets' },
  { name: 'Planning', href: '/planning' },
  { name: 'Doc<PERSON>', href: 'https://docs.solera.work', external: true },
]

interface NavigationMenuProps {
  connected?: boolean
  tokenBalance?: number | null
  isLoadingBalance?: boolean
  formatBalance?: (balance: number | null) => string
}

export function NavigationMenu({ 
  connected = false, 
  tokenBalance = null, 
  isLoadingBalance = false, 
  formatBalance = (balance) => balance?.toFixed(2) || '0.00'
}: NavigationMenuProps) {
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const mobileMenuRef = useRef<HTMLDivElement>(null)

  // Click outside handler for mobile menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        setIsMobileMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden md:flex items-center space-x-6">
        {navigation.map((item) => {
          const isActive = pathname === item.href && !item.external

          if (item.external) {
            return (
              <a
                key={item.name}
                href={item.href}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs font-medium text-gray-400 hover:text-gray-300 transition-colors"
              >
                {item.name}
              </a>
            )
          }

          return (
            <Link
              key={item.name}
              href={item.href}
              className={`text-xs font-medium transition-colors relative ${
                isActive
                  ? 'text-white'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              {item.name}
              {isActive && (
                <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white"></div>
              )}
            </Link>
          )
        })}
      </nav>

      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="md:hidden w-7 h-7 bg-white rounded flex items-center justify-center hover:bg-gray-100 transition-colors"
      >
        {isMobileMenuOpen ? (
          <X className="w-4 h-4 text-black" />
        ) : (
          <Menu className="w-4 h-4 text-black" />
        )}
      </button>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div
          ref={mobileMenuRef}
          className="dropdown-menu md:hidden absolute top-16 left-4 right-4 py-2 z-40 rounded"
        >
          {/* Mobile Navigation */}
          <div className="py-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href && !item.external

              if (item.external) {
                return (
                  <a
                    key={item.name}
                    href={item.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block px-4 py-3 text-sm font-medium transition-colors hover:bg-gray-800"
                    style={{ color: 'var(--text-secondary)' }}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </a>
                )
              }

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block px-4 py-3 text-sm font-medium transition-colors hover:bg-gray-800 ${
                    isActive ? 'bg-gray-800' : ''
                  }`}
                  style={{
                    color: isActive ? 'var(--text-primary)' : 'var(--text-secondary)'
                  }}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              )
            })}
          </div>

          {/* Mobile Token Balance (if connected) */}
          {connected && (
            <div className="border-t px-4 py-3" style={{ borderColor: 'var(--border-primary)' }}>
              <div className="text-xs mb-1" style={{ color: 'var(--text-secondary)' }}>Token Balance</div>
              <div className="text-sm" style={{ color: 'var(--text-primary)' }}>
                {isLoadingBalance ? (
                  <span className="animate-pulse">Loading...</span>
                ) : (
                  <span>{formatBalance(tokenBalance)} RA</span>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </>
  )
}
