#!/usr/bin/env node

/**
 * Clean build script that suppresses bigint binding warnings
 * while preserving other important warnings and errors
 */

const { spawn } = require('child_process')
const path = require('path')

// Store original console methods
const originalWarn = console.warn
const originalError = console.error
const originalLog = console.log

// Filter function for bigint warnings
function shouldSuppressWarning(message) {
  if (typeof message !== 'string') return false

  const suppressPatterns = [
    'bigint: Failed to load bindings, pure JS will be used',
    'try npm run rebuild?'
  ]

  return suppressPatterns.some(pattern => message.includes(pattern))
}

// Override console methods to filter warnings
console.warn = (...args) => {
  const message = args.join(' ')
  if (!shouldSuppressWarning(message)) {
    originalWarn.apply(console, args)
  }
}

console.error = (...args) => {
  const message = args.join(' ')
  if (!shouldSuppressWarning(message)) {
    originalError.apply(console, args)
  }
}

// Run the actual build process
const buildProcess = spawn('npx', ['next', 'build'], {
  stdio: ['inherit', 'pipe', 'pipe'],
  shell: true,
  cwd: process.cwd()
})

// Handle stdout (normal output)
buildProcess.stdout.on('data', (data) => {
  const output = data.toString()
  const lines = output.split('\n')

  lines.forEach(line => {
    if (line.trim() && !shouldSuppressWarning(line)) {
      originalLog(line)
    }
  })
})

// Handle stderr (error output)
buildProcess.stderr.on('data', (data) => {
  const output = data.toString()
  const lines = output.split('\n')

  lines.forEach(line => {
    if (line.trim() && !shouldSuppressWarning(line)) {
      originalError(line)
    }
  })
})

// Handle process completion
buildProcess.on('close', (code) => {
  if (code === 0) {
    originalLog('\n✅ Build completed successfully (bigint warnings suppressed)')
  } else {
    originalError(`\n❌ Build failed with exit code ${code}`)
  }
  process.exit(code)
})

// Handle process errors
buildProcess.on('error', (error) => {
  originalError('Build process error:', error)
  process.exit(1)
})
