// Configuration for handling bigint operations in Solana applications
// This helps resolve native binding warnings

// Ensure BigInt is available globally
if (typeof BigInt === 'undefined') {
  // Fallback for environments without native BigInt support
  global.BigInt = require('big-integer')
}

// Configure JSON serialization for BigInt
if (typeof BigInt !== 'undefined') {
  // Add toJSON method to BigInt prototype for proper serialization
  (BigInt.prototype as any).toJSON = function() {
    return this.toString()
  }
}

// Helper functions for safe BigInt operations
export const BigIntUtils = {
  // Safe BigInt creation
  from: (value: string | number | bigint): bigint => {
    try {
      return BigInt(value)
    } catch (error) {
      console.warn('BigInt conversion failed, using 0:', error)
      return BigInt(0)
    }
  },

  // Safe BigInt to string conversion
  toString: (value: bigint): string => {
    try {
      return value.toString()
    } catch (error) {
      console.warn('BigInt toString failed:', error)
      return '0'
    }
  },

  // Safe BigInt to number conversion (with overflow protection)
  toNumber: (value: bigint): number => {
    try {
      const num = Number(value)
      if (num > Number.MAX_SAFE_INTEGER) {
        console.warn('BigInt value exceeds MAX_SAFE_INTEGER, precision may be lost')
      }
      return num
    } catch (error) {
      console.warn('BigInt toNumber failed:', error)
      return 0
    }
  },

  // Check if value is safe for number conversion
  isSafeInteger: (value: bigint): boolean => {
    try {
      const num = Number(value)
      return Number.isSafeInteger(num)
    } catch {
      return false
    }
  }
}

// Export for use in Solana operations
export default BigIntUtils
