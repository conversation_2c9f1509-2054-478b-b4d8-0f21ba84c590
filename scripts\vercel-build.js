#!/usr/bin/env node

/**
 * Vercel-optimized build script
 * Handles Solana dependencies and suppresses warnings for clean deployment
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

// Environment setup for Vercel
process.env.SKIP_ENV_VALIDATION = 'true'
process.env.NODE_ENV = 'production'

// Ensure required directories exist
const requiredDirs = ['.next', 'node_modules']
requiredDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    console.log(`Creating directory: ${dir}`)
    fs.mkdirSync(dir, { recursive: true })
  }
})

// Filter function for warnings
function shouldSuppressWarning(message) {
  if (typeof message !== 'string') return false
  
  const suppressPatterns = [
    'bigint: Failed to load bindings, pure JS will be used',
    'try npm run rebuild?',
    'Module not found: Can\'t resolve \'pino-pretty\'',
    'Failed to load bindings',
    'pure JS will be used'
  ]
  
  return suppressPatterns.some(pattern => message.includes(pattern))
}

// Store original console methods
const originalWarn = console.warn
const originalError = console.error
const originalLog = console.log

// Override console methods to filter warnings
console.warn = (...args) => {
  const message = args.join(' ')
  if (!shouldSuppressWarning(message)) {
    originalWarn.apply(console, args)
  }
}

console.error = (...args) => {
  const message = args.join(' ')
  if (!shouldSuppressWarning(message)) {
    originalError.apply(console, args)
  }
}

// Run Prisma generate if schema exists
const prismaSchemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma')
if (fs.existsSync(prismaSchemaPath)) {
  console.log('🔧 Generating Prisma client...')
  try {
    const prismaGenerate = spawn('npx', ['prisma', 'generate'], {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd()
    })
    
    prismaGenerate.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Prisma client generated successfully')
        runBuild()
      } else {
        console.warn('⚠️ Prisma generate failed, continuing with build...')
        runBuild()
      }
    })
  } catch (error) {
    console.warn('⚠️ Prisma generate error, continuing with build...', error.message)
    runBuild()
  }
} else {
  runBuild()
}

function runBuild() {
  console.log('🚀 Starting Next.js build...')
  
  // Run the actual build process
  const buildProcess = spawn('npx', ['next', 'build'], {
    stdio: ['inherit', 'pipe', 'pipe'],
    shell: true,
    cwd: process.cwd(),
    env: {
      ...process.env,
      SKIP_ENV_VALIDATION: 'true',
      NODE_ENV: 'production'
    }
  })

  // Handle stdout (normal output)
  buildProcess.stdout.on('data', (data) => {
    const output = data.toString()
    const lines = output.split('\n')
    
    lines.forEach(line => {
      if (line.trim() && !shouldSuppressWarning(line)) {
        originalLog(line)
      }
    })
  })

  // Handle stderr (error output)
  buildProcess.stderr.on('data', (data) => {
    const output = data.toString()
    const lines = output.split('\n')
    
    lines.forEach(line => {
      if (line.trim() && !shouldSuppressWarning(line)) {
        originalError(line)
      }
    })
  })

  // Handle process completion
  buildProcess.on('close', (code) => {
    if (code === 0) {
      originalLog('\n✅ Vercel build completed successfully!')
      originalLog('🎉 Ready for deployment')
    } else {
      originalError(`\n❌ Build failed with exit code ${code}`)
    }
    process.exit(code)
  })

  // Handle process errors
  buildProcess.on('error', (error) => {
    originalError('Build process error:', error)
    process.exit(1)
  })
}
