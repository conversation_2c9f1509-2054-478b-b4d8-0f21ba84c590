# Database
DATABASE_URL="mongodb://localhost:27017/solera-staking"

# Solana Network Configuration
# Options: "devnet", "testnet", "mainnet-beta"

# === DEVNET CONFIGURATION (for development) ===
NEXT_PUBLIC_SOLANA_NETWORK="devnet"
NEXT_PUBLIC_RPC_ENDPOINT="https://devnet.helius-rpc.com/?api-key=YOUR_API_KEY"
NEXT_PUBLIC_DEV_MODE="true"

# === MAINNET CONFIGURATION (for production) ===
# NEXT_PUBLIC_SOLANA_NETWORK="mainnet-beta"
# NEXT_PUBLIC_RPC_ENDPOINT="https://solana-api.projectserum.com"
# NEXT_PUBLIC_DEV_MODE="false"

# Token Configuration
# Solera RA Token Addresses:
# Devnet: 4aQNjPD9Zy9B86csxdXjUZHF4MpLWEM2z5psTyzDrQsQ
# Mainnet: 2jPF5RY4B3jtJb4iAwRZ5J68WLLu4uaaBZ4wpjV29YYA
NEXT_PUBLIC_SOLERA_RA_TOKEN_ADDRESS="4aQNjPD9Zy9B86csxdXjUZHF4MpLWEM2z5psTyzDrQsQ"

# App
NEXT_PUBLIC_APP_NAME="Solera Staking"

# Instructions:
# 1. Copy this file to .env.local
# 2. Choose either DEVNET or MAINNET configuration
# 3. For DEVNET: Enable DEV_MODE for mock token balances
# 4. For MAINNET: Disable DEV_MODE for real token balances
# 5. Replace YOUR_API_KEY with your actual Helius API key
