/* Hero Component Dynamic Flare Effects */

/* Custom Gradient Utilities */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Grid Pattern Background */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Custom Animations */

/* Slow Pulse Animation */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

/* Float Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-10px) scale(1.02);
    opacity: 0.6;
  }
  66% {
    transform: translateY(5px) scale(0.98);
    opacity: 0.4;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Breathe Animation */
@keyframes breathe {
  0%, 100% {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

.animate-breathe {
  animation: breathe 5s ease-in-out infinite;
}

/* Shimmer Animation */
@keyframes shimmer {
  0% {
    opacity: 0;
    transform: translateY(-100%) rotate(12deg);
  }
  50% {
    opacity: 0.4;
    transform: translateY(0%) rotate(12deg);
  }
  100% {
    opacity: 0;
    transform: translateY(100%) rotate(12deg);
  }
}

.animate-shimmer {
  animation: shimmer 8s ease-in-out infinite;
}

/* Delayed Shimmer Animation */
@keyframes shimmer-delayed {
  0% {
    opacity: 0;
    transform: translateY(-100%) rotate(-12deg);
  }
  50% {
    opacity: 0.3;
    transform: translateY(0%) rotate(-12deg);
  }
  100% {
    opacity: 0;
    transform: translateY(100%) rotate(-12deg);
  }
}

.animate-shimmer-delayed {
  animation: shimmer-delayed 10s ease-in-out infinite 2s;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  /* Reduce glow orb sizes on mobile */
  .hero-glow-primary {
    width: 240px !important;
    height: 240px !important;
  }

  .hero-glow-secondary {
    width: 200px !important;
    height: 200px !important;
  }

  .hero-glow-accent {
    width: 160px !important;
    height: 160px !important;
  }

  /* Hide light rays on mobile for performance */
  .hero-light-ray {
    display: none;
  }

  /* Reduce grid pattern opacity on mobile */
  .bg-grid-pattern {
    opacity: 0.02 !important;
  }

  /* Mobile banner optimizations */
  .hero-banner-enhanced {
    max-width: calc(100vw - 2rem);
    min-height: 44px;
  }

  /* Ensure button text is always visible on mobile */
  .hero-banner-enhanced button {
    white-space: nowrap;
    overflow: visible;
  }
}

/* Accessibility - Respect reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .animate-pulse-slow,
  .animate-float,
  .animate-breathe,
  .animate-shimmer,
  .animate-shimmer-delayed {
    animation: none;
  }

  /* Provide static fallback with subtle opacity */
  .animate-pulse-slow {
    opacity: 0.6;
  }

  .animate-float {
    opacity: 0.4;
  }

  .animate-breathe {
    opacity: 0.3;
  }

  .animate-shimmer,
  .animate-shimmer-delayed {
    opacity: 0.2;
  }
}

/* Performance Optimizations */
.hero-flare-container {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

.hero-flare-element {
  will-change: transform, opacity;
  transform-style: preserve-3d;
}

/* Enhanced Glow Effects for Interactive Elements */
.hero-stats-glow {
  position: relative;
}

.hero-stats-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    rgba(6, 182, 212, 0.1),
    rgba(59, 130, 246, 0.1),
    rgba(20, 184, 166, 0.1)
  );
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.hero-stats-glow:hover::before {
  opacity: 1;
}

/* Subtle Banner Enhancement */
.hero-banner-enhanced {
  position: relative;
  overflow: hidden;
}

.hero-banner-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: banner-shine 3s ease-in-out infinite;
}

@keyframes banner-shine {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}
