# API Integration Documentation

## Overview

The Solera Staking application includes a comprehensive HTTP client built with Axios for professional-grade API integration. This system provides robust error handling, retry logic, authentication, and analytics tracking.

## Architecture

### Core Components

1. **API Client** (`src/lib/api.ts`) - Centralized HTTP client with interceptors
2. **API Services** (`src/services/api.ts`) - Service layer for different API endpoints
3. **TypeScript Types** (`src/types/api.ts`) - Comprehensive type definitions
4. **Error Boundary** (`src/components/common/ErrorBoundary.tsx`) - Error handling component

## Configuration

### Environment Variables

```bash
# API Configuration
NEXT_PUBLIC_API_BASE_URL="http://localhost:3001/api"
NEXT_PUBLIC_API_TIMEOUT="10000"
NEXT_PUBLIC_API_RETRY_ATTEMPTS="3"
```

### Features

#### 🔧 **Request/Response Interceptors**
- Automatic authentication headers
- Request/response logging
- Error transformation
- Retry logic with exponential backoff

#### 🔄 **Retry Logic**
- Configurable retry attempts (default: 3)
- Exponential backoff delays
- Retry on network errors and 5xx responses
- Rate limiting handling (429 responses)

#### 🔐 **Authentication**
- Automatic wallet address headers
- Session management
- Request tracking with unique IDs

#### 📊 **Analytics Integration**
- Wallet connection tracking
- Token balance fetch analytics
- User interaction events
- Error tracking

## API Services

### UserService
```typescript
// Get user profile
const profile = await UserService.getProfile(walletAddress)

// Update profile
const updated = await UserService.updateProfile(walletAddress, updates)

// Create new profile
const newProfile = await UserService.createProfile(walletAddress, data)
```

### WalletService
```typescript
// Record wallet connection
const response = await WalletService.recordConnection({
  walletAddress,
  walletType: 'phantom',
  network: 'devnet',
  timestamp: new Date().toISOString()
})

// Record disconnection
await WalletService.recordDisconnection(walletAddress)

// Get session info
const session = await WalletService.getSessionInfo(walletAddress)
```

### AnalyticsService
```typescript
// Track custom event
await AnalyticsService.trackEvent({
  event: 'button_click',
  properties: { button: 'stake' },
  userId: walletAddress,
  timestamp: new Date().toISOString()
})

// Track page view
await AnalyticsService.trackPageView('/dashboard', walletAddress)

// Track wallet connection
await AnalyticsService.trackWalletConnection(walletAddress, 'phantom')
```

### TokenService
```typescript
// Get enhanced token balance
const balance = await TokenService.getTokenBalance({
  walletAddress,
  tokenAddress,
  network: 'devnet'
})

// Get token metadata
const metadata = await TokenService.getTokenMetadata(tokenAddress)
```

## Error Handling

### Error Types

The system defines specific error types for different scenarios:

```typescript
// Network errors
NetworkError - Connection issues, server errors
TimeoutError - Request timeouts
ValidationError - Input validation failures
AuthenticationError - Auth/permission issues
RateLimitError - Rate limiting (429 responses)
```

### Error Recovery

1. **Automatic Retry** - Network errors and server errors
2. **Exponential Backoff** - Increasing delays between retries
3. **Graceful Degradation** - Continue with basic functionality if APIs fail
4. **User Feedback** - Clear error messages and recovery options

### Error Boundary Usage

```typescript
import { ErrorBoundary } from '@/components/common/ErrorBoundary'

// Wrap components that might throw errors
<ErrorBoundary>
  <WalletButton />
</ErrorBoundary>

// Custom fallback UI
<ErrorBoundary fallback={<div>Custom error UI</div>}>
  <MyComponent />
</ErrorBoundary>
```

## Integration with WalletButton

The WalletButton component demonstrates full API integration:

### Features Implemented

1. **User Profile Loading** - Fetches user data on wallet connection
2. **Connection Analytics** - Tracks wallet connection events
3. **Token Balance Analytics** - Tracks successful/failed balance fetches
4. **Session Management** - Records connection/disconnection events
5. **Error Handling** - Graceful fallback when APIs are unavailable

### Console Output

When wallet connects, you'll see:
```
🔗 API Client initialized: { baseURL: "http://localhost:3001/api", ... }
📤 API Request [req_123]: { method: "POST", url: "/wallet/connect", ... }
👤 Fetching user profile for: 1234...
📈 Tracking analytics event: { event: "wallet_connected", ... }
✅ User profile loaded successfully
```

## Testing

### Development Mode

The application gracefully handles API unavailability:

1. **API Unavailable** - Shows "Profile unavailable" but continues functioning
2. **Network Errors** - Automatic retry with exponential backoff
3. **Timeout Errors** - Clear timeout handling with fallback
4. **Rate Limiting** - Respects retry-after headers

### Mock API Responses

For testing without a backend, the system logs all API calls and continues with basic functionality.

## Production Considerations

### Performance
- Request/response compression
- Connection pooling
- Request deduplication
- Caching strategies

### Security
- HTTPS enforcement
- Request signing
- Rate limiting compliance
- Sensitive data sanitization

### Monitoring
- Request/response logging
- Error rate tracking
- Performance metrics
- User analytics

## Best Practices

1. **Always handle errors gracefully** - Don't break user experience
2. **Use TypeScript types** - Ensure type safety for all API calls
3. **Log appropriately** - Debug info in development, minimal in production
4. **Respect rate limits** - Implement proper backoff strategies
5. **Sanitize logs** - Remove sensitive information from logs

## Future Enhancements

- [ ] Request caching with TTL
- [ ] WebSocket integration for real-time updates
- [ ] Offline support with request queuing
- [ ] Advanced error recovery strategies
- [ ] Performance monitoring integration
- [ ] A/B testing framework integration
