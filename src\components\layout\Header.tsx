'use client'

import { useState } from 'react'
import { useWallet } from '@solana/wallet-adapter-react'
import { Logo } from '@/components/ui/Logo'
import { NavigationMenu } from '@/components/ui/NavigationMenu'
import { WalletButton } from '@/components/ui/WalletButton'

export function Header() {
  const { connected } = useWallet()
  const [tokenBalance, setTokenBalance] = useState<number | null>(null)
  const [isLoadingBalance, setIsLoadingBalance] = useState(false)

  // Format token balance for display
  const formatBalance = (balance: number | null): string => {
    if (balance === null) return '0.00'
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(balance)
  }

  // Handle balance changes from WalletButton
  const handleBalanceChange = (balance: number | null, isLoading: boolean) => {
    setTokenBalance(balance)
    setIsLoadingBalance(isLoading)
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50">
      <div className="px-6 py-3.5 flex items-center justify-between" style={{ background: 'var(--bg-primary)' }}>
        {/* Left side - Logo and Navigation */}
        <div className="flex items-center space-x-6">
          <Logo />
          <NavigationMenu
            connected={connected}
            tokenBalance={tokenBalance}
            isLoadingBalance={isLoadingBalance}
            formatBalance={formatBalance}
          />
        </div>

        {/* Right side - Controls */}
        <WalletButton onBalanceChange={handleBalanceChange} />
      </div>
    </header>
  )
}
