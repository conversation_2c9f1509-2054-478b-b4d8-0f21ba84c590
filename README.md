# Solera Staking Platform

A modern Solana meme token staking platform built with Next.js, TypeScript, Tailwind CSS, Prisma ORM, and MongoDB.

## Features

- 🚀 **Modern Tech Stack**: Next.js 14, TypeScript, Tailwind CSS
- 🔗 **Solana Integration**: Wallet connection and blockchain interactions
- 💾 **Database**: MongoDB with Prisma ORM
- 🎨 **Beautiful UI**: Dark theme with gradient backgrounds
- 📱 **Responsive Design**: Works on all devices
- 🔒 **Type Safety**: Full TypeScript support

## Tech Stack

- **Frontend**: Next.js 14 with App Router
- **Styling**: Tailwind CSS
- **Database**: MongoDB with Prisma ORM
- **Blockchain**: Solana Web3.js
- **Wallet**: Solana Wallet Adapter
- **Language**: TypeScript

## Getting Started

### Prerequisites

- Node.js 18+
- MongoDB (local or cloud)
- Git

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd solera-staking
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your configuration:

```env
DATABASE_URL="mongodb://localhost:27017/solera-staking"
NEXT_PUBLIC_SOLANA_NETWORK="devnet"
NEXT_PUBLIC_RPC_ENDPOINT="https://api.devnet.solana.com"
```

4. Set up the database:

```bash
npx prisma generate
npx prisma db push
```

5. Run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## Project Structure

```
src/
├── app/                 # Next.js App Router pages
├── components/          # React components
│   ├── hero/            # Hero section components
│   ├── layout/          # Layout components
│   ├── protocols/       # Protocol components
│   ├── providers/       # Context providers
│   ├── staking/         # Staking components
│   └── ui/              # Reusable UI components
├── lib/                 # Utility functions
├── types/               # TypeScript type definitions
└── styles/              # Global styles
```

## Key Components

- **Hero Section**: Overview of staking statistics with integrated wallet banner
- **Protocol Explorer**: Discover new DeFi protocols
- **One-Click Staking**: Easy token staking interface
- **Wallet Integration**: Solana wallet connection
- **Responsive Design**: Mobile-first approach

## Database Schema

The application uses Prisma with MongoDB for data management:

- **Users**: Wallet addresses and user data
- **Tokens**: Supported tokens for staking
- **Staking Pools**: Available staking opportunities
- **Staking Positions**: User staking records
- **Protocols**: DeFi protocol information

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Database Commands

- `npx prisma generate` - Generate Prisma client
- `npx prisma db push` - Push schema to database
- `npx prisma studio` - Open Prisma Studio

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
