import { NextResponse } from 'next/server'

// Dynamic import to prevent build-time database access
async function getPrismaClient() {
  try {
    const { prisma } = await import('@/lib/prisma')
    return prisma
  } catch (error) {
    console.error('Failed to load Prisma client:', error)
    return null
  }
}

export async function GET() {
  try {
    // Check if we're in build time (no database access needed)
    if (process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not available during build' },
        { status: 503 }
      )
    }

    const prisma = await getPrismaClient()
    if (!prisma) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 503 }
      )
    }

    const tokens = await prisma.token.findMany({
      include: {
        stakingPools: true,
      },
    })

    return NextResponse.json(tokens)
  } catch (error) {
    console.error('Error fetching tokens:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tokens' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    // Check if we're in build time (no database access needed)
    if (process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not available during build' },
        { status: 503 }
      )
    }

    const prisma = await getPrismaClient()
    if (!prisma) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 503 }
      )
    }

    const body = await request.json()
    const { symbol, name, logoUrl, decimals, mintAddress } = body

    const token = await prisma.token.create({
      data: {
        symbol,
        name,
        logoUrl,
        decimals,
        mintAddress,
      },
    })

    return NextResponse.json(token, { status: 201 })
  } catch (error) {
    console.error('Error creating token:', error)
    return NextResponse.json(
      { error: 'Failed to create token' },
      { status: 500 }
    )
  }
}
