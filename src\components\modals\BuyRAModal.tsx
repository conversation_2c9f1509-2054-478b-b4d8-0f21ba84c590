'use client'

import { useEffect, useState } from 'react'
import { X, ExternalLink } from 'lucide-react'
import Image from 'next/image'

interface Platform {
  name: string
  url: string
  imagePath: string
  description: string
}

interface BuyRAModalProps {
  isOpen: boolean
  onClose: () => void
}

const platforms: Platform[] = [
  {
    name: 'DEXTools',
    url: 'https://www.dextools.io/app/en/token/solera?t=1748529793623',
    imagePath: '/buy/dextool.png',
    description: 'Advanced trading analytics'
  },
  {
    name: 'DEXScreener',
    url: 'https://dexscreener.com/solana/gjcatx94fs1adw1hevwfh8ach9kmxancrw3xwpxr6prz',
    imagePath: '/buy/dexscreener.png',
    description: 'Real-time DEX data'
  },
  {
    name: 'Raydium',
    url: 'https://raydium.io/swap/?inputMint=sol&outputMint=2jPF5RY4B3jtJb4iAwRZ5J68WLLu4uaaBZ4wpjV29YYA',
    imagePath: '/buy/raydium.png',
    description: 'Automated market maker'
  },
  {
    name: 'Meteora',
    url: 'https://meteora.ag',
    imagePath: '/buy/meteora.png',
    description: 'Dynamic liquidity protocol'
  }
]

export function BuyRAModal({ isOpen, onClose }: BuyRAModalProps) {
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set())

  // Handle escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      // Prevent body scrolling
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  // Handle platform click
  const handlePlatformClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  // Handle image error
  const handleImageError = (platformName: string) => {
    setImageErrors(prev => new Set(prev).add(platformName))
  }

  // Handle backdrop click
  const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (event.target === event.currentTarget) {
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" />

      {/* Modal */}
      <div
        className="relative w-full max-w-md max-h-[90vh] overflow-y-auto rounded bg-gray-900 shadow-2xl"
        style={{ background: '#1a1a1a', border: '1px solid #333' }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b" style={{ borderColor: '#333' }}>
          <div>
            <h2 className="text-lg font-medium text-white">Buy RA Tokens</h2>
          </div>
          <button
            onClick={onClose}
            className="w-6 h-6 flex items-center justify-center transition-colors hover:bg-gray-800 rounded"
            aria-label="Close modal"
          >
            <X className="w-4 h-4 text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="py-2">
          {platforms.map((platform) => (
            <button
              key={platform.name}
              onClick={() => handlePlatformClick(platform.url)}
              className="w-full flex items-center px-4 py-3 hover:bg-gray-800/30 transition-colors text-left group border-b border-gray-800/50 last:border-b-0"
            >
              <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3" style={{ background: '#2a2a2a' }}>
                {imageErrors.has(platform.name) ? (
                  <ExternalLink className="w-5 h-5 text-gray-400" />
                ) : (
                  <Image
                    src={platform.imagePath}
                    alt={platform.name}
                    width={24}
                    height={24}
                    className="w-6 h-6 object-contain"
                    onError={() => handleImageError(platform.name)}
                  />
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-white text-base">
                      {platform.name}
                    </h3>
                    <p className="text-sm text-gray-400 mt-0.5">
                      {platform.description}
                    </p>
                  </div>
                  <div className="text-right">
                    <ExternalLink className="w-4 h-4 text-gray-500 group-hover:text-gray-400 transition-colors" />
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Disclaimer */}
        <div className="mx-4 mb-4 p-3 rounded" style={{ background: '#2a1f0f', border: '1px solid #4a3728' }}>
          <p className="text-xs text-yellow-200">
            <strong>Disclaimer:</strong> Always verify the authenticity of the platform before making any transactions.
            Ensure you're on the official website and never share your private keys or seed phrases.
          </p>
        </div>
      </div>
    </div>
  )
}
