/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '*.solera.work',
        pathname: '/**',
      },
    ],
  },
  // Vercel-specific configuration
  output: 'standalone',
  // Skip build-time data fetching for API routes
  generateBuildId: async () => {
    return 'vercel-build-' + Date.now()
  },
  webpack: (config, { isServer }) => {
    // Handle bigint and crypto modules for Solana
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      crypto: require.resolve('crypto-browserify'),
      stream: require.resolve('stream-browserify'),
      url: require.resolve('url'),
      zlib: require.resolve('browserify-zlib'),
      http: require.resolve('stream-http'),
      https: require.resolve('https-browserify'),
      assert: require.resolve('assert'),
      os: require.resolve('os-browserify/browser'),
      path: require.resolve('path-browserify'),
      // Handle pino-pretty for Vercel
      'pino-pretty': false,
    }

    // Ignore node-specific modules in client-side bundles
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        child_process: false,
        fs: false,
        net: false,
        tls: false,
      }
    }

    // Handle bigint serialization and suppress warnings
    config.experiments = {
      ...config.experiments,
      topLevelAwait: true,
    }

    // Suppress bigint binding warnings
    const originalWarn = console.warn
    console.warn = (...args) => {
      if (args[0] && args[0].includes && args[0].includes('bigint: Failed to load bindings')) {
        return // Suppress bigint binding warnings
      }
      originalWarn.apply(console, args)
    }

    // Add module rules for better handling of native modules
    config.module.rules.push({
      test: /\.node$/,
      use: 'node-loader',
    })

    // Optimize for Solana dependencies
    config.optimization = {
      ...config.optimization,
      moduleIds: 'deterministic',
    }

    return config
  },
  // Suppress bigint warnings during build
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 2,
  },
  // Experimental features for better crypto support
  experimental: {
    esmExternals: 'loose',
  },
}

module.exports = nextConfig
