import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import {
  ApiResponse,
  ApiError,
  ApiConfig,
  RequestConfig,
  NetworkError,
  TimeoutError,
  ValidationError,
  AuthenticationError,
  RateLimitError,
  ApiErrorType
} from '@/types/api'

class ApiClient {
  private client: AxiosInstance
  private config: ApiConfig
  private retryCount: Map<string, number> = new Map()

  constructor() {
    this.config = this.getConfig()
    this.client = this.createClient()
    this.setupInterceptors()
  }

  private getConfig(): ApiConfig {
    return {
      baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000/api',
      timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '10000'),
      retryAttempts: parseInt(process.env.NEXT_PUBLIC_API_RETRY_ATTEMPTS || '3'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      }
    }
  }

  private createClient(): AxiosInstance {
    const client = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: this.config.headers,
    })

    console.log('🔗 API Client initialized:', {
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      retryAttempts: this.config.retryAttempts
    })

    return client
  }

  private setupInterceptors(): void {
    // Request Interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add authentication headers if wallet is connected
        const walletAddress = this.getWalletAddress()
        if (walletAddress) {
          config.headers['X-Wallet-Address'] = walletAddress
        }

        // Add request timestamp
        config.headers['X-Request-Timestamp'] = new Date().toISOString()

        // Add request ID for tracking
        const requestId = this.generateRequestId()
        config.headers['X-Request-ID'] = requestId

        console.log(`📤 API Request [${requestId}]:`, {
          method: config.method?.toUpperCase(),
          url: config.url,
          baseURL: config.baseURL,
          headers: this.sanitizeHeaders(config.headers)
        })

        return config
      },
      (error) => {
        console.error('❌ Request interceptor error:', error)
        return Promise.reject(error)
      }
    )

    // Response Interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        const requestId = response.config.headers['X-Request-ID']
        console.log(`📥 API Response [${requestId}]:`, {
          status: response.status,
          statusText: response.statusText,
          data: response.data
        })

        // Reset retry count on successful response
        if (requestId) {
          this.retryCount.delete(requestId)
        }

        return response
      },
      async (error: AxiosError) => {
        return this.handleResponseError(error)
      }
    )
  }

  private async handleResponseError(error: AxiosError): Promise<never> {
    const requestId = error.config?.headers?.['X-Request-ID'] as string
    const currentRetries = this.retryCount.get(requestId) || 0

    console.error(`❌ API Error [${requestId}]:`, {
      status: error.response?.status,
      statusText: error.response?.statusText,
      message: error.message,
      retryAttempt: currentRetries + 1
    })

    // Check if we should retry
    if (this.shouldRetry(error, currentRetries)) {
      this.retryCount.set(requestId, currentRetries + 1)

      // Exponential backoff delay
      const delay = Math.pow(2, currentRetries) * 1000
      console.log(`🔄 Retrying request [${requestId}] in ${delay}ms...`)

      await this.delay(delay)
      return this.client.request(error.config!)
    }

    // Clean up retry count
    this.retryCount.delete(requestId)

    // Transform error to our custom error types
    const apiError = this.transformError(error)
    return Promise.reject(apiError)
  }

  private shouldRetry(error: AxiosError, currentRetries: number): boolean {
    if (currentRetries >= this.config.retryAttempts) {
      return false
    }

    // Retry on network errors
    if (!error.response) {
      return true
    }

    // Retry on server errors (5xx) and rate limiting (429)
    const status = error.response.status
    return status >= 500 || status === 429
  }

  private transformError(error: AxiosError): ApiErrorType {
    const status = error.response?.status
    const data = error.response?.data as any

    // Network/Connection errors
    if (!error.response) {
      const networkError: NetworkError = new Error(error.message) as NetworkError
      networkError.code = 'NETWORK_ERROR'
      networkError.name = 'NetworkError'
      return networkError
    }

    // Timeout errors
    if (error.code === 'ECONNABORTED') {
      const timeoutError: TimeoutError = new Error('Request timeout') as TimeoutError
      timeoutError.code = 'TIMEOUT_ERROR'
      timeoutError.timeout = this.config.timeout
      timeoutError.name = 'TimeoutError'
      return timeoutError
    }

    // Rate limiting
    if (status === 429) {
      const rateLimitError: RateLimitError = new Error('Rate limit exceeded') as RateLimitError
      rateLimitError.code = 'RATE_LIMIT_ERROR'
      rateLimitError.retryAfter = parseInt(error.response.headers['retry-after'] || '60')
      rateLimitError.name = 'RateLimitError'
      return rateLimitError
    }

    // Authentication errors
    if (status === 401 || status === 403) {
      const authError: AuthenticationError = new Error(data?.message || 'Authentication failed') as AuthenticationError
      authError.code = 'AUTHENTICATION_ERROR'
      authError.walletAddress = this.getWalletAddress()
      authError.name = 'AuthenticationError'
      return authError
    }

    // Validation errors
    if (status === 400 && data?.fields) {
      const validationError: ValidationError = new Error(data.message || 'Validation failed') as ValidationError
      validationError.code = 'VALIDATION_ERROR'
      validationError.fields = data.fields
      validationError.name = 'ValidationError'
      return validationError
    }

    // Generic network error for other cases
    const networkError: NetworkError = new Error(data?.message || error.message) as NetworkError
    networkError.code = 'NETWORK_ERROR'
    networkError.status = status
    networkError.response = data
    networkError.name = 'NetworkError'
    return networkError
  }

  private getWalletAddress(): string | null {
    // This will be set by the wallet component when connected
    return (globalThis as any).__SOLERA_WALLET_ADDRESS__ || null
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers }
    // Remove sensitive information from logs
    if (sanitized.Authorization) {
      sanitized.Authorization = '[REDACTED]'
    }
    return sanitized
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // Public API methods
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.get(url, config)
    return response.data
  }

  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.post(url, data, config)
    return response.data
  }

  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.put(url, data, config)
    return response.data
  }

  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.delete(url, config)
    return response.data
  }

  public async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.patch(url, data, config)
    return response.data
  }

  // Utility methods
  public setWalletAddress(address: string | null): void {
    (globalThis as any).__SOLERA_WALLET_ADDRESS__ = address
    console.log('🔐 Wallet address set for API requests:', address ? `${address.slice(0, 8)}...` : 'null')
  }

  public getBaseURL(): string {
    return this.config.baseURL
  }

  public getTimeout(): number {
    return this.config.timeout
  }
}

// Create singleton instance
export const apiClient = new ApiClient()

// Export default instance
export default apiClient
