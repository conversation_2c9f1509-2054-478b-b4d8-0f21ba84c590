'use client'

import { ChevronRight } from 'lucide-react'
import { TokenStakingInfo } from '@/types'
import { formatNumber, formatPercentage } from '@/lib/utils'

interface StakingCardProps {
  tokenInfo: TokenStakingInfo
  viewMode: 'grid' | 'list'
}

export function StakingCard({ tokenInfo, viewMode }: StakingCardProps) {
  const { token, pool, pendingRewards, flexibleSavings, lockedSavings, flexibleApy, lockedApy, duration } = tokenInfo

  if (viewMode === 'list') {
    return (
      <div className="bg-card-gradient rounded-lg p-4 border border-gray-700/50 hover:border-gray-600/50 transition-all duration-200 hover:scale-[1.02]">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-cyan-500 to-blue-600 flex items-center justify-center">
              <span className="text-white font-bold text-sm">{token.symbol.charAt(0)}</span>
            </div>
            <div>
              <h3 className="font-medium text-white">{token.symbol}</h3>
              <p className="text-sm text-gray-400">{token.name}</p>
            </div>
          </div>

          <div className="flex items-center space-x-8">
            <div className="text-center">
              <p className="text-xs text-gray-400">Pending Rewards</p>
              <p className="text-sm font-medium text-white">{formatNumber(pendingRewards, 6)}</p>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-400">Flexible</p>
              <p className="text-sm font-medium text-green-400">{formatPercentage(flexibleApy)}</p>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-400">Locked</p>
              <p className="text-sm font-medium text-green-400">{formatPercentage(lockedApy)}</p>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-400">Duration</p>
              <p className="text-sm font-medium text-white">{duration}</p>
            </div>
            <ChevronRight className="w-5 h-5 text-gray-400" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="card-container p-4 cursor-pointer">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-cyan-500 to-blue-600 flex items-center justify-center">
            <span className="text-white font-bold text-xs">{token.symbol.charAt(0)}</span>
          </div>
          <div>
            <h3 className="font-medium text-white text-sm">{token.symbol}</h3>
          </div>
        </div>
        <ChevronRight className="w-4 h-4 text-gray-400" />
      </div>

      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-400">Flexible savings</span>
          <span className="text-xs font-medium text-gray-400">APR</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-400">Flexible</span>
          <span className="text-xs font-medium text-green-400">{formatPercentage(flexibleApy)}</span>
        </div>

        <div className="flex justify-between items-center mt-3">
          <span className="text-xs text-gray-400">Wealth management</span>
          <span className="text-xs font-medium text-gray-400">APR</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-400">{duration}</span>
          <span className="text-xs font-medium text-green-400">{formatPercentage(lockedApy)}</span>
        </div>
      </div>
    </div>
  )
}
