'use client'

import React, { useMemo } from 'react'
import { ConnectionProvider, WalletProvider as SolanaWalletProvider } from '@solana/wallet-adapter-react'
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base'
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui'
import { PhantomWalletAdapter, SolflareWalletAdapter } from '@solana/wallet-adapter-wallets'
import { clusterApiUrl } from '@solana/web3.js'

// Import wallet adapter CSS
require('@solana/wallet-adapter-react-ui/styles.css')

interface WalletProviderProps {
  children: React.ReactNode
}

export function WalletProvider({ children }: WalletProviderProps) {
  // The network can be set to 'devnet', 'testnet', or 'mainnet-beta'
  // Changed to Mainnet for Solera RA token
  const network = WalletAdapterNetwork.Mainnet

  // You can also provide a custom RPC endpoint
  // Using alternative RPC endpoints to avoid 403 rate limiting issues
  const endpoint = useMemo(() => {
    // Use environment variable if available, otherwise use reliable public RPC
    if (process.env.NEXT_PUBLIC_RPC_ENDPOINT) {
      return process.env.NEXT_PUBLIC_RPC_ENDPOINT
    }

    // Alternative reliable RPC endpoints for Mainnet
    const alternativeEndpoints = [
      'https://devnet.helius-rpc.com/?api-key=1188c76d-5e21-4879-909c-8eabf12d550e',
      'https://rpc.ankr.com/solana',
      'https://solana-mainnet.rpc.extrnode.com',
      clusterApiUrl(network) // Fallback to default
    ]

    // For now, use the first alternative endpoint
    const selectedEndpoint = alternativeEndpoints[0]
    console.log('Using Solana RPC endpoint:', selectedEndpoint)
    return selectedEndpoint
  }, [network])

  const wallets = useMemo(
    () => [
      new PhantomWalletAdapter(),
      new SolflareWalletAdapter(),
    ],
    []
  )

  return (
    <ConnectionProvider endpoint={endpoint}>
      <SolanaWalletProvider wallets={wallets} autoConnect>
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </SolanaWalletProvider>
    </ConnectionProvider>
  )
}
