'use client'

import React, { useMemo } from 'react'
import { ConnectionProvider, WalletProvider as SolanaWalletProvider } from '@solana/wallet-adapter-react'
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base'
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui'
import { PhantomWalletAdapter, SolflareWalletAdapter } from '@solana/wallet-adapter-wallets'
import { clusterApiUrl } from '@solana/web3.js'

// Import wallet adapter CSS
require('@solana/wallet-adapter-react-ui/styles.css')

interface WalletProviderProps {
  children: React.ReactNode
}

export function WalletProvider({ children }: WalletProviderProps) {
  // Get network from environment variable, default to mainnet for Solera RA token
  const network = useMemo(() => {
    const envNetwork = process.env.NEXT_PUBLIC_SOLANA_NETWORK
    console.log('Environment Network Setting:', envNetwork)

    switch (envNetwork) {
      case 'devnet':
        return WalletAdapterNetwork.Devnet
      case 'testnet':
        return WalletAdapterNetwork.Testnet
      case 'mainnet-beta':
      case 'mainnet':
        return WalletAdapterNetwork.Mainnet
      default:
        console.warn('Invalid or missing NEXT_PUBLIC_SOLANA_NETWORK, defaulting to Mainnet')
        return WalletAdapterNetwork.Mainnet
    }
  }, [])

  // RPC endpoint configuration with proper fallbacks
  const endpoint = useMemo(() => {
    // Primary: Use environment variable if available
    if (process.env.NEXT_PUBLIC_RPC_ENDPOINT) {
      console.log('Using RPC endpoint from environment:', process.env.NEXT_PUBLIC_RPC_ENDPOINT)
      return process.env.NEXT_PUBLIC_RPC_ENDPOINT
    }

    // Fallback: Use reliable public RPC endpoints based on network
    const fallbackEndpoints = {
      [WalletAdapterNetwork.Mainnet]: [
        'https://solana-api.projectserum.com',
        'https://rpc.ankr.com/solana',
        'https://solana-mainnet.rpc.extrnode.com',
        clusterApiUrl(WalletAdapterNetwork.Mainnet)
      ],
      [WalletAdapterNetwork.Devnet]: [
        clusterApiUrl(WalletAdapterNetwork.Devnet)
      ],
      [WalletAdapterNetwork.Testnet]: [
        clusterApiUrl(WalletAdapterNetwork.Testnet)
      ]
    }

    const selectedEndpoint = fallbackEndpoints[network][0]
    console.log('Using fallback RPC endpoint:', selectedEndpoint)
    return selectedEndpoint
  }, [network])

  const wallets = useMemo(
    () => [
      new PhantomWalletAdapter(),
      new SolflareWalletAdapter(),
    ],
    []
  )

  return (
    <ConnectionProvider endpoint={endpoint}>
      <SolanaWalletProvider wallets={wallets} autoConnect>
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </SolanaWalletProvider>
    </ConnectionProvider>
  )
}
