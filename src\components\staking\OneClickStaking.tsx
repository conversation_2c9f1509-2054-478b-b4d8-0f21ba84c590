'use client'

import { useState } from 'react'
import { Search, Filter, Grid, List, ChevronDown } from 'lucide-react'
import { StakingCard } from './StakingCard'
import { TokenStakingInfo } from '@/types'

export function OneClickStaking() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedNetwork, setSelectedNetwork] = useState('All networks')
  const [activeTab, setActiveTab] = useState('Single-asset')

  const tabs = ['Single-asset', 'Multi-asset', 'Stablecoins', 'Points', 'Restake']

  // Mock staking data matching the image
  const stakingTokens: TokenStakingInfo[] = [
    {
      token: { id: '1', symbol: 'BTC', name: 'Bitcoin', decimals: 8, mintAddress: 'btc-mint' },
      pool: { id: '1', tokenId: '1', name: 'BTC Pool', apy: 1.4, totalStaked: 1000000, minStake: 0.001, isActive: true },
      pendingRewards: 0.00012,
      flexibleSavings: 1.4,
      lockedSavings: 0.8,
      flexibleApy: 1.4,
      lockedApy: 0.8,
      duration: '30 Days'
    },
    {
      token: { id: '2', symbol: 'USDT', name: 'Tether USD', decimals: 6, mintAddress: 'usdt-mint' },
      pool: { id: '2', tokenId: '2', name: 'USDT Pool', apy: 7.0, totalStaked: 5000000, minStake: 10, isActive: true },
      pendingRewards: 12.45,
      flexibleSavings: 7.0,
      lockedSavings: 4.2,
      flexibleApy: 7.0,
      lockedApy: 4.2,
      duration: '30 Days'
    },
    {
      token: { id: '3', symbol: 'ATOM', name: 'Cosmos', decimals: 6, mintAddress: 'atom-mint' },
      pool: { id: '3', tokenId: '3', name: 'ATOM Pool', apy: 10.3, totalStaked: 2000000, minStake: 1, isActive: true },
      pendingRewards: 2.34,
      flexibleSavings: 10.3,
      lockedSavings: 13.6,
      flexibleApy: 10.3,
      lockedApy: 13.6,
      duration: '30 Days'
    },
    {
      token: { id: '4', symbol: 'TON', name: 'Toncoin', decimals: 9, mintAddress: 'ton-mint' },
      pool: { id: '4', tokenId: '4', name: 'TON Pool', apy: 0.7, totalStaked: 800000, minStake: 10, isActive: true },
      pendingRewards: 5.67,
      flexibleSavings: 0.7,
      lockedSavings: 11.1,
      flexibleApy: 0.7,
      lockedApy: 11.1,
      duration: '30 Days'
    },
    {
      token: { id: '5', symbol: 'ETH', name: 'Ethereum', decimals: 18, mintAddress: 'eth-mint' },
      pool: { id: '5', tokenId: '5', name: 'ETH Pool', apy: 1.5, totalStaked: 1500000, minStake: 0.01, isActive: true },
      pendingRewards: 0.0234,
      flexibleSavings: 1.5,
      lockedSavings: 7.4,
      flexibleApy: 1.5,
      lockedApy: 7.4,
      duration: '60 Days'
    },
    {
      token: { id: '6', symbol: 'USDC', name: 'USD Coin', decimals: 6, mintAddress: 'usdc-mint' },
      pool: { id: '6', tokenId: '6', name: 'USDC Pool', apy: 0.0, totalStaked: 3000000, minStake: 10, isActive: true },
      pendingRewards: 8.91,
      flexibleSavings: 0.0,
      lockedSavings: 4.7,
      flexibleApy: 0.0,
      lockedApy: 4.7,
      duration: '30 Days'
    },
    {
      token: { id: '7', symbol: 'AXS', name: 'Axie Infinity', decimals: 18, mintAddress: 'axs-mint' },
      pool: { id: '7', tokenId: '7', name: 'AXS Pool', apy: 11.8, totalStaked: 500000, minStake: 1, isActive: true },
      pendingRewards: 1.23,
      flexibleSavings: 11.8,
      lockedSavings: 3.0,
      flexibleApy: 11.8,
      lockedApy: 3.0,
      duration: '30 Days'
    },
    {
      token: { id: '8', symbol: 'SOL', name: 'Solana', decimals: 9, mintAddress: 'sol-mint' },
      pool: { id: '8', tokenId: '8', name: 'SOL Pool', apy: 4.5, totalStaked: 2500000, minStake: 0.1, isActive: true },
      pendingRewards: 0.456,
      flexibleSavings: 4.5,
      lockedSavings: 9.2,
      flexibleApy: 4.5,
      lockedApy: 9.2,
      duration: '30 Days'
    }
  ]

  const filteredTokens = stakingTokens.filter(token =>
    token.token.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
    token.token.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-2 p-6 rounded-2xl" style={{ background: 'var(--bg-primary)' }}>
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-white">One-Click Staking</h2>
        <div className="flex items-center space-x-2">
          <button className="button-secondary p-2">
            <Search className="w-4 h-4" style={{ color: 'var(--text-secondary)' }} />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'list' ? 'button-primary' : 'button-secondary'
            }`}
          >
            <List className="w-4 h-4" style={{ color: viewMode === 'list' ? 'var(--text-primary)' : 'var(--text-secondary)' }} />
          </button>
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'grid' ? 'button-primary' : 'button-secondary'
            }`}
          >
            <Grid className="w-4 h-4" style={{ color: viewMode === 'grid' ? 'var(--text-primary)' : 'var(--text-secondary)' }} />
          </button>
          <button className="button-secondary p-2">
            <Filter className="w-4 h-4" style={{ color: 'var(--text-secondary)' }} />
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex items-center space-x-6 border-b border-gray-700/30 pb-4">
        {tabs.map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`text-sm font-medium transition-colors ${
              activeTab === tab
                ? 'text-white'
                : 'text-gray-400 hover:text-gray-300'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Network Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center">
            <span className="text-white text-xs font-bold">S</span>
          </div>
          <div className="w-6 h-6 rounded-full bg-teal-500 flex items-center justify-center">
            <span className="text-white text-xs font-bold">E</span>
          </div>
          <button className="button-secondary flex items-center space-x-2 px-3 py-1 text-sm">
            <span style={{ color: 'var(--text-secondary)' }}>All networks</span>
            <ChevronDown className="w-3 h-3" style={{ color: 'var(--text-secondary)' }} />
          </button>
        </div>

        <div className="flex items-center space-x-4">
          <button className="button-secondary flex items-center space-x-2 px-3 py-1 text-sm">
            <span style={{ color: 'var(--text-secondary)' }}>All tokens</span>
            <ChevronDown className="w-3 h-3" style={{ color: 'var(--text-secondary)' }} />
          </button>
        </div>
      </div>

      {/* Staking Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {filteredTokens.map((tokenInfo) => (
          <StakingCard
            key={tokenInfo.token.id}
            tokenInfo={tokenInfo}
            viewMode={viewMode}
          />
        ))}
      </div>
    </div>
  )
}
