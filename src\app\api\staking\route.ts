import { NextResponse } from 'next/server'

// Dynamic import to prevent build-time database access
async function getPrismaClient() {
  try {
    const { prisma } = await import('@/lib/prisma')
    return prisma
  } catch (error) {
    console.error('Failed to load Prisma client:', error)
    return null
  }
}

export async function GET() {
  try {
    // Check if we're in build time (no database access needed)
    if (process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not available during build' },
        { status: 503 }
      )
    }

    const prisma = await getPrismaClient()
    if (!prisma) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 503 }
      )
    }

    const stakingPools = await prisma.stakingPool.findMany({
      include: {
        token: true,
        stakingPositions: {
          include: {
            user: true,
          },
        },
      },
      where: {
        isActive: true,
      },
    })

    return NextResponse.json(stakingPools)
  } catch (error) {
    console.error('Error fetching staking pools:', error)
    return NextResponse.json(
      { error: 'Failed to fetch staking pools' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    // Check if we're in build time (no database access needed)
    if (process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not available during build' },
        { status: 503 }
      )
    }

    const prisma = await getPrismaClient()
    if (!prisma) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 503 }
      )
    }

    const body = await request.json()
    const { userId, tokenId, poolId, amount } = body

    const stakingPosition = await prisma.stakingPosition.create({
      data: {
        userId,
        tokenId,
        poolId,
        amount,
      },
      include: {
        token: true,
        pool: true,
        user: true,
      },
    })

    return NextResponse.json(stakingPosition, { status: 201 })
  } catch (error) {
    console.error('Error creating staking position:', error)
    return NextResponse.json(
      { error: 'Failed to create staking position' },
      { status: 500 }
    )
  }
}
