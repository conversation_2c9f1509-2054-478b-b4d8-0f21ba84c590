@tailwind base;
@tailwind components;
@tailwind utilities;

/* Solera Font Family */
@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Thin.woff2') format('woff2'),
       url('/fonts/solera/Solera-Thin.woff') format('woff');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Thin-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Thin-Italic.woff') format('woff');
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Light.woff2') format('woff2'),
       url('/fonts/solera/Solera-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Light-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Light-Italic.woff') format('woff');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Regular.woff2') format('woff2'),
       url('/fonts/solera/Solera-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Medium.woff2') format('woff2'),
       url('/fonts/solera/Solera-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Medium-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Medium-Italic.woff') format('woff');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Bold.woff2') format('woff2'),
       url('/fonts/solera/Solera-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Bold-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Bold-Italic.woff') format('woff');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Black.woff2') format('woff2'),
       url('/fonts/solera/Solera-Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Black-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Black-Italic.woff') format('woff');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: var(--bg-secondary);
  font-family: 'Solera', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Gradient backgrounds */
.bg-dark-gradient {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.bg-card-gradient {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

/* Glass effect */
.glass {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

/* Optical Pattern Background */
.bg-optical-pattern {
  background:
    radial-gradient(circle at 25% 25%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 25%, rgba(20, 184, 166, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 25% 75%, rgba(6, 182, 212, 0.08) 0%, transparent 50%),
    linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  background-size: 400px 400px, 600px 600px, 800px 800px, 500px 500px, 100% 100%;
  background-position: 0 0, 100px 100px, 200px 200px, 300px 300px, 0 0;
  animation: opticalShift 20s ease-in-out infinite;
}

@keyframes opticalShift {
  0%, 100% {
    background-position: 0 0, 100px 100px, 200px 200px, 300px 300px, 0 0;
  }
  50% {
    background-position: 100px 100px, 200px 200px, 300px 300px, 400px 400px, 0 0;
  }
}

/* Hide Scrollbar Globally */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
  display: none; /* WebKit */
}

html, body {
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
}

/* Design System - Consistent Color Palette */
:root {
  --bg-primary: #131313;        /* gray-900 */
  --bg-secondary: #181818;      /* gray-800 */
  --bg-tertiary: #374151;       /* gray-700 */
  --border-primary: #374151;    /* gray-700 */
  --border-secondary: #4b5563;  /* gray-600 */
  --text-primary: #ffffff;      /* white */
  --text-secondary: #9ca3af;    /* gray-400 */
  --text-tertiary: #6b7280;     /* gray-500 */
  --accent-cyan: #06b6d4;       /* cyan-500 */
  --accent-blue: #3b82f6;       /* blue-500 */
  --accent-teal: #14b8a6;       /* teal-500 */
  --custom-brown: #813817;      /* Custom brown color */
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Component Styles */
.dropdown-menu {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-xl);
  color: var(--text-primary);
}

.card-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-smooth);
}

.card-container:hover {
  border-color: var(--border-secondary);
  transform: translateY(-2px);
}

.button-primary {
  background: linear-gradient(to right, var(--accent-cyan), var(--accent-blue));
  border: none;
  border-radius: 9999px;
  color: var(--text-primary);
  font-weight: 500;
  transition: var(--transition-smooth);
}

.button-primary:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.button-secondary {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  color: var(--text-primary);
  transition: var(--transition-smooth);
}

.button-secondary:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-secondary);
}

/* Solana Wallet Adapter Custom Styling */
.wallet-adapter-modal-wrapper {
  background: var(--text-primary) !important;
  border-radius: 4px !important;
  align-items:flex-start !important;
}

.wallet-adapter-modal-title {
  color: var(--bg-secondary) !important;
  font-size: 0.75rem !important;
  margin: 0 !important;
  padding: 1rem !important;
  text-align: left !important;
}

.wallet-adapter-modal-button-close {
  color: var(--bg-secondary) !important;
  font-size: 0.9rem !important;
  border-radius: 4px !important;
  color: var(--text-primary) !important;
  background-color: var(--bg-primary) !important;
  padding: 7px;
}

.wallet-adapter-button {
  background: var(--text-primary) !important;
  border: none !important;
  border-radius: 4px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  letter-spacing: 0.025em !important;
  padding: 1rem 0.75rem !important;
  height: 1.5rem !important;
  min-height: 1.5rem !important;
  box-shadow: none !important;
  transition: var(--transition-smooth) !important;
  color: var(--bg-secondary) !important;
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}

.wallet-adapter-button:hover {
  background: #f3f4f6 !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.wallet-adapter-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.5) !important;
}

.wallet-adapter-button:active {
  transform: scale(0.98) !important;
}

/* Override any specific wallet button styling */
.wallet-adapter-button-trigger {
  background: var(--text-primary) !important;
  border: none !important;
  border-radius: 4px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  letter-spacing: 0.025em !important;
  height: 1.5rem !important;
  min-height: 1.5rem !important;
  box-shadow: none !important;
  transition: var(--transition-smooth) !important;
  color: #1f2937 !important;
}

.wallet-adapter-button-trigger:hover {
  background: #f3f4f6 !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}
