@tailwind base;
@tailwind components;
@tailwind utilities;

/* Solera Font Family */
@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Thin.woff2') format('woff2'),
       url('/fonts/solera/Solera-Thin.woff') format('woff');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Thin-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Thin-Italic.woff') format('woff');
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Light.woff2') format('woff2'),
       url('/fonts/solera/Solera-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Light-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Light-Italic.woff') format('woff');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Regular.woff2') format('woff2'),
       url('/fonts/solera/Solera-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Medium.woff2') format('woff2'),
       url('/fonts/solera/Solera-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Medium-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Medium-Italic.woff') format('woff');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Bold.woff2') format('woff2'),
       url('/fonts/solera/Solera-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Bold-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Bold-Italic.woff') format('woff');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Black.woff2') format('woff2'),
       url('/fonts/solera/Solera-Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Black-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Black-Italic.woff') format('woff');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: var(--bg-secondary);
  font-family: 'Solera', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Gradient backgrounds */
.bg-dark-gradient {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.bg-card-gradient {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

/* Glass effect */
.glass {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

/* Optical Pattern Background */
.bg-optical-pattern {
  background:
    radial-gradient(circle at 25% 25%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 25%, rgba(20, 184, 166, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 25% 75%, rgba(6, 182, 212, 0.08) 0%, transparent 50%),
    linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  background-size: 400px 400px, 600px 600px, 800px 800px, 500px 500px, 100% 100%;
  background-position: 0 0, 100px 100px, 200px 200px, 300px 300px, 0 0;
  animation: opticalShift 20s ease-in-out infinite;
}

@keyframes opticalShift {
  0%, 100% {
    background-position: 0 0, 100px 100px, 200px 200px, 300px 300px, 0 0;
  }
  50% {
    background-position: 100px 100px, 200px 200px, 300px 300px, 400px 400px, 0 0;
  }
}

/* Hide Scrollbar Globally */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
  display: none; /* WebKit */
}

html, body {
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
}

/* Design System - Consistent Color Palette */
:root {
  --bg-primary: #131313;        /* gray-900 */
  --bg-secondary: #181818;      /* gray-800 */
  --bg-tertiary: #374151;       /* gray-700 */
  --border-primary: #374151;    /* gray-700 */
  --border-secondary: #4b5563;  /* gray-600 */
  --text-primary: #ffffff;      /* white */
  --text-secondary: #9ca3af;    /* gray-400 */
  --text-tertiary: #6b7280;     /* gray-500 */
  --accent-cyan: #06b6d4;       /* cyan-500 */
  --accent-blue: #3b82f6;       /* blue-500 */
  --accent-teal: #14b8a6;       /* teal-500 */
  --custom-brown: #813817;      /* Custom brown color */
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Component Styles */
.dropdown-menu {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-xl);
  color: var(--text-primary);
}

.card-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-smooth);
}

.card-container:hover {
  border-color: var(--border-secondary);
  transform: translateY(-2px);
}

.button-primary {
  background: linear-gradient(to right, var(--accent-cyan), var(--accent-blue));
  border: none;
  border-radius: 9999px;
  color: var(--text-primary);
  font-weight: 500;
  transition: var(--transition-smooth);
}

.button-primary:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.button-secondary {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  color: var(--text-primary);
  transition: var(--transition-smooth);
}

.button-secondary:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-secondary);
}

/* Solana Wallet Adapter Custom Styling */
.wallet-adapter-button {
  background: #ffffff !important;
  border: none !important;
  border-radius: 4px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  letter-spacing: 0.025em !important;
  padding: 0.25rem 0.75rem !important;
  height: 1.5rem !important;
  min-height: 1.5rem !important;
  box-shadow: none !important;
  transition: var(--transition-smooth) !important;
  color: #1f2937 !important;
}

.wallet-adapter-button:hover {
  background: #f3f4f6 !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.wallet-adapter-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.5) !important;
}

.wallet-adapter-button:active {
  transform: scale(0.98) !important;
}

/* Override any specific wallet button styling */
.wallet-adapter-button-trigger {
  background: #ffffff !important;
  border: none !important;
  border-radius: 4px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  letter-spacing: 0.025em !important;
  padding: 0.25rem 0.75rem !important;
  height: 1.5rem !important;
  min-height: 1.5rem !important;
  box-shadow: none !important;
  transition: var(--transition-smooth) !important;
  color: #1f2937 !important;
}

.wallet-adapter-button-trigger:hover {
  background: #f3f4f6 !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Solana Wallet Adapter Modal Styling - Updated with White Background and Reduced Font Sizes */
.wallet-adapter-modal {
  font-family: 'Solera', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif !important;
  display: block !important;
  visibility: visible !important;
}

/* Modal Backdrop */
.wallet-adapter-modal-overlay {
  background: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(12px) !important;
  z-index: 9999 !important;
  display: flex !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  align-items: center !important;
  justify-content: center !important;
  visibility: visible !important;
  padding: 1rem !important;
}

/* Modal Container - White Background */
.wallet-adapter-modal-container {
  background: #ffffff !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 16px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  max-width: 420px !important;
  width: 100% !important;
  max-height: 85vh !important;
  overflow: hidden !important;
  position: relative !important;
  display: block !important;
  visibility: visible !important;
  z-index: 10000 !important;
}

/* Modal Header - Reduced Font Size */
.wallet-adapter-modal-title {
  color: #1f2937 !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  font-family: 'Solera', sans-serif !important;
  margin: 0 !important;
  padding: 2rem 2rem 0.5rem 2rem !important;
  text-align: left !important;
  line-height: 1.5 !important;
}

/* Close Button */
.wallet-adapter-modal-button-close {
  background: transparent !important;
  border: none !important;
  color: #6b7280 !important;
  cursor: pointer !important;
  padding: 0.75rem !important;
  position: absolute !important;
  right: 1.5rem !important;
  top: 1.5rem !important;
  border-radius: 8px !important;
  transition: var(--transition-smooth) !important;
  width: 2.5rem !important;
  height: 2.5rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.wallet-adapter-modal-button-close:hover {
  background: #f3f4f6 !important;
  color: #1f2937 !important;
  transform: scale(1.05) !important;
}

.wallet-adapter-modal-button-close svg {
  width: 1.25rem !important;
  height: 1.25rem !important;
}

/* Modal Body */
.wallet-adapter-modal-list {
  padding: 0 2rem 2rem 2rem !important;
  margin: 0 !important;
  list-style: none !important;
  max-height: 65vh !important;
  overflow-y: auto !important;
}

/* Wallet Option Items */
.wallet-adapter-modal-list li {
  margin: 0 0 0.5rem 0 !important;
  padding: 0 !important;
}

.wallet-adapter-modal-list li:last-child {
  margin-bottom: 0 !important;
}

/* Wallet Buttons */
.wallet-adapter-modal-list .wallet-adapter-button {
  background: #f9fafb !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 12px !important;
  color: #1f2937 !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  font-family: 'Solera', sans-serif !important;
  font-size: 0.8125rem !important;
  font-weight: 500 !important;
  height: auto !important;
  min-height: 3.5rem !important;
  padding: 1rem 1.25rem !important;
  text-align: left !important;
  transition: var(--transition-smooth) !important;
  width: 100% !important;
  justify-content: flex-start !important;
  gap: 1rem !important;
  position: relative !important;
}

.wallet-adapter-modal-list .wallet-adapter-button:hover {
  background: #f3f4f6 !important;
  border-color: #d1d5db !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.3) !important;
}

.wallet-adapter-modal-list .wallet-adapter-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 4px 12px -4px rgba(0, 0, 0, 0.2) !important;
}

/* Wallet Icons */
.wallet-adapter-modal-list .wallet-adapter-button img {
  width: 2rem !important;
  height: 2rem !important;
  border-radius: 50% !important;
  flex-shrink: 0 !important;
  object-fit: cover !important;
  border: 2px solid rgba(0, 0, 0, 0.1) !important;
}

/* Wallet Names - Reduced Font Size */
.wallet-adapter-modal-list .wallet-adapter-button .wallet-adapter-button-label {
  color: #1f2937 !important;
  font-family: 'Solera', sans-serif !important;
  font-size: 0.8125rem !important;
  font-weight: 500 !important;
  flex: 1 !important;
  line-height: 1.4 !important;
}

/* Status Indicators - Reduced Font Size */
.wallet-adapter-modal-list .wallet-adapter-button .wallet-adapter-button-status {
  color: #6b7280 !important;
  font-size: 0.6875rem !important;
  font-weight: 400 !important;
  margin-left: auto !important;
  background: rgba(6, 182, 212, 0.1) !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 6px !important;
  font-family: 'Solera', sans-serif !important;
}

/* Detected/Installed Status */
.wallet-adapter-modal-list .wallet-adapter-button[data-detected="true"] .wallet-adapter-button-status {
  color: var(--accent-cyan) !important;
  background: rgba(6, 182, 212, 0.15) !important;
}

/* Loading State */
.wallet-adapter-modal-list .wallet-adapter-button[disabled] {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

.wallet-adapter-modal-list .wallet-adapter-button[disabled]:hover {
  background: #f9fafb !important;
  border-color: #e5e7eb !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Scrollbar Styling for Modal */
.wallet-adapter-modal-list::-webkit-scrollbar {
  width: 4px !important;
  display: block !important;
}

.wallet-adapter-modal-list::-webkit-scrollbar-track {
  background: #ffffff !important;
}

.wallet-adapter-modal-list::-webkit-scrollbar-thumb {
  background: #e5e7eb !important;
  border-radius: 2px !important;
}

.wallet-adapter-modal-list::-webkit-scrollbar-thumb:hover {
  background: #d1d5db !important;
}

/* Modal Animation */
.wallet-adapter-modal-container {
  animation: modalFadeIn 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Modal Footer - Reduced Font Size */
.wallet-adapter-modal-list::after {
  content: "By connecting your wallet, you agree to Solera's Terms & Privacy Policy" !important;
  display: block !important;
  margin-top: 1.5rem !important;
  padding-top: 1.5rem !important;
  border-top: 1px solid #e5e7eb !important;
  color: #6b7280 !important;
  font-size: 0.6875rem !important;
  font-family: 'Solera', sans-serif !important;
  line-height: 1.4 !important;
  text-align: center !important;
}

/* Focus States */
.wallet-adapter-modal-list .wallet-adapter-button:focus {
  outline: none !important;
  border-color: var(--accent-cyan) !important;
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2) !important;
}

/* Mobile Responsive */
@media (max-width: 640px) {
  .wallet-adapter-modal-overlay {
    padding: 0.5rem !important;
  }

  .wallet-adapter-modal-container {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    border-radius: 12px !important;
  }

  .wallet-adapter-modal-title {
    font-size: 0.875rem !important;
    padding: 1.5rem 1.5rem 0.5rem 1.5rem !important;
  }

  .wallet-adapter-modal-list {
    padding: 0 1.5rem 1.5rem 1.5rem !important;
  }

  .wallet-adapter-modal-list .wallet-adapter-button {
    min-height: 3rem !important;
    padding: 0.875rem 1rem !important;
    font-size: 0.75rem !important;
    gap: 0.875rem !important;
  }

  .wallet-adapter-modal-list .wallet-adapter-button .wallet-adapter-button-label {
    font-size: 0.75rem !important;
  }

  .wallet-adapter-modal-list .wallet-adapter-button .wallet-adapter-button-status {
    font-size: 0.625rem !important;
  }

  .wallet-adapter-modal-list .wallet-adapter-button img {
    width: 1.75rem !important;
    height: 1.75rem !important;
  }

  .wallet-adapter-modal-button-close {
    right: 1.25rem !important;
    top: 1.25rem !important;
    width: 2.25rem !important;
    height: 2.25rem !important;
  }

  .wallet-adapter-modal-button-close svg {
    width: 1.125rem !important;
    height: 1.125rem !important;
  }

  .wallet-adapter-modal-list::after {
    font-size: 0.625rem !important;
  }
}
