@tailwind base;
@tailwind components;
@tailwind utilities;

/* Solera Font Family */
@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Thin.woff2') format('woff2'),
       url('/fonts/solera/Solera-Thin.woff') format('woff');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Thin-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Thin-Italic.woff') format('woff');
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Light.woff2') format('woff2'),
       url('/fonts/solera/Solera-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Light-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Light-Italic.woff') format('woff');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Regular.woff2') format('woff2'),
       url('/fonts/solera/Solera-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Medium.woff2') format('woff2'),
       url('/fonts/solera/Solera-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Medium-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Medium-Italic.woff') format('woff');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Bold.woff2') format('woff2'),
       url('/fonts/solera/Solera-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Bold-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Bold-Italic.woff') format('woff');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Black.woff2') format('woff2'),
       url('/fonts/solera/Solera-Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Solera';
  src: url('/fonts/solera/Solera-Black-Italic.woff2') format('woff2'),
       url('/fonts/solera/Solera-Black-Italic.woff') format('woff');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: var(--bg-secondary);
  font-family: 'Solera', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Gradient backgrounds */
.bg-dark-gradient {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.bg-card-gradient {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

/* Glass effect */
.glass {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

/* Optical Pattern Background */
.bg-optical-pattern {
  background:
    radial-gradient(circle at 25% 25%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 25%, rgba(20, 184, 166, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 25% 75%, rgba(6, 182, 212, 0.08) 0%, transparent 50%),
    linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  background-size: 400px 400px, 600px 600px, 800px 800px, 500px 500px, 100% 100%;
  background-position: 0 0, 100px 100px, 200px 200px, 300px 300px, 0 0;
  animation: opticalShift 20s ease-in-out infinite;
}

@keyframes opticalShift {
  0%, 100% {
    background-position: 0 0, 100px 100px, 200px 200px, 300px 300px, 0 0;
  }
  50% {
    background-position: 100px 100px, 200px 200px, 300px 300px, 400px 400px, 0 0;
  }
}

/* Hide Scrollbar Globally */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
  display: none; /* WebKit */
}

html, body {
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
}

/* Design System - Consistent Color Palette */
:root {
  --bg-primary: #131313;        /* gray-900 */
  --bg-secondary: #181818;      /* gray-800 */
  --bg-tertiary: #374151;       /* gray-700 */
  --border-primary: #374151;    /* gray-700 */
  --border-secondary: #4b5563;  /* gray-600 */
  --text-primary: #ffffff;      /* white */
  --text-secondary: #9ca3af;    /* gray-400 */
  --text-tertiary: #6b7280;     /* gray-500 */
  --accent-cyan: #06b6d4;       /* cyan-500 */
  --accent-blue: #3b82f6;       /* blue-500 */
  --accent-teal: #14b8a6;       /* teal-500 */
  --custom-brown: #813817;      /* Custom brown color */
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Component Styles */
.dropdown-menu {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-xl);
  color: var(--text-primary);
}

.card-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-smooth);
}

.card-container:hover {
  border-color: var(--border-secondary);
  transform: translateY(-2px);
}

.button-primary {
  background: linear-gradient(to right, var(--accent-cyan), var(--accent-blue));
  border: none;
  border-radius: 9999px;
  color: var(--text-primary);
  font-weight: 500;
  transition: var(--transition-smooth);
}

.button-primary:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.button-secondary {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 0.5rem;
  color: var(--text-primary);
  transition: var(--transition-smooth);
}

.button-secondary:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-secondary);
}

/* Solana Wallet Adapter Custom Styling */
.wallet-adapter-button {
  background: #ffffff !important;
  border: none !important;
  border-radius: 4px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  letter-spacing: 0.025em !important;
  padding: 0.25rem 0.75rem !important;
  height: 1.5rem !important;
  min-height: 1.5rem !important;
  box-shadow: none !important;
  transition: var(--transition-smooth) !important;
  color: #1f2937 !important;
}

.wallet-adapter-button:hover {
  background: #f3f4f6 !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.wallet-adapter-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.5) !important;
}

.wallet-adapter-button:active {
  transform: scale(0.98) !important;
}

/* Override any specific wallet button styling */
.wallet-adapter-button-trigger {
  background: #ffffff !important;
  border: none !important;
  border-radius: 4px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  letter-spacing: 0.025em !important;
  padding: 0.25rem 0.75rem !important;
  height: 1.5rem !important;
  min-height: 1.5rem !important;
  box-shadow: none !important;
  transition: var(--transition-smooth) !important;
  color: #1f2937 !important;
}

.wallet-adapter-button-trigger:hover {
  background: #f3f4f6 !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Solana Wallet Adapter Modal Styling - Solera Design System */
.wallet-adapter-modal {
  font-family: 'Solera', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif !important;
}

/* Modal Backdrop */
.wallet-adapter-modal-overlay {
  background: rgba(0, 0, 0, 0.75) !important;
  backdrop-filter: blur(8px) !important;
  z-index: 9999 !important;
}

/* Modal Container */
.wallet-adapter-modal-container {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 4px !important;
  box-shadow: var(--shadow-xl) !important;
  max-width: 400px !important;
  width: 90% !important;
  max-height: 80vh !important;
  overflow: hidden !important;
}

/* Modal Header */
.wallet-adapter-modal-title {
  color: var(--text-primary) !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  font-family: 'Solera', sans-serif !important;
  margin: 0 !important;
  padding: 1.5rem 1.5rem 0 1.5rem !important;
  text-align: center !important;
}

/* Close Button */
.wallet-adapter-modal-button-close {
  background: transparent !important;
  border: none !important;
  color: var(--text-secondary) !important;
  cursor: pointer !important;
  padding: 0.5rem !important;
  position: absolute !important;
  right: 1rem !important;
  top: 1rem !important;
  border-radius: 4px !important;
  transition: var(--transition-smooth) !important;
  width: 2rem !important;
  height: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.wallet-adapter-modal-button-close:hover {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

.wallet-adapter-modal-button-close svg {
  width: 1rem !important;
  height: 1rem !important;
}

/* Modal Body */
.wallet-adapter-modal-list {
  padding: 1.5rem !important;
  margin: 0 !important;
  list-style: none !important;
  max-height: 60vh !important;
  overflow-y: auto !important;
}

/* Wallet Option Items */
.wallet-adapter-modal-list li {
  margin: 0 0 0.75rem 0 !important;
  padding: 0 !important;
}

.wallet-adapter-modal-list li:last-child {
  margin-bottom: 0 !important;
}

/* Wallet Buttons */
.wallet-adapter-modal-list .wallet-adapter-button {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 4px !important;
  color: var(--text-primary) !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  font-family: 'Solera', sans-serif !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  height: auto !important;
  min-height: 3rem !important;
  padding: 0.75rem 1rem !important;
  text-align: left !important;
  transition: var(--transition-smooth) !important;
  width: 100% !important;
  justify-content: flex-start !important;
  gap: 0.75rem !important;
}

.wallet-adapter-modal-list .wallet-adapter-button:hover {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-secondary) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.wallet-adapter-modal-list .wallet-adapter-button:active {
  transform: translateY(0) !important;
  box-shadow: var(--shadow-lg) !important;
}

/* Wallet Icons */
.wallet-adapter-modal-list .wallet-adapter-button img {
  width: 1.5rem !important;
  height: 1.5rem !important;
  border-radius: 2px !important;
  flex-shrink: 0 !important;
}

/* Wallet Names */
.wallet-adapter-modal-list .wallet-adapter-button .wallet-adapter-button-label {
  color: var(--text-primary) !important;
  font-family: 'Solera', sans-serif !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  flex: 1 !important;
}

/* Status Indicators */
.wallet-adapter-modal-list .wallet-adapter-button .wallet-adapter-button-status {
  color: var(--text-secondary) !important;
  font-size: 0.75rem !important;
  font-weight: 400 !important;
  margin-left: auto !important;
}

/* Detected/Installed Status */
.wallet-adapter-modal-list .wallet-adapter-button[data-detected="true"] .wallet-adapter-button-status {
  color: var(--accent-cyan) !important;
}

/* Loading State */
.wallet-adapter-modal-list .wallet-adapter-button[disabled] {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

.wallet-adapter-modal-list .wallet-adapter-button[disabled]:hover {
  background: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Scrollbar Styling for Modal */
.wallet-adapter-modal-list::-webkit-scrollbar {
  width: 4px !important;
}

.wallet-adapter-modal-list::-webkit-scrollbar-track {
  background: var(--bg-primary) !important;
}

.wallet-adapter-modal-list::-webkit-scrollbar-thumb {
  background: var(--border-primary) !important;
  border-radius: 2px !important;
}

.wallet-adapter-modal-list::-webkit-scrollbar-thumb:hover {
  background: var(--border-secondary) !important;
}

/* Modal Animation */
.wallet-adapter-modal-container {
  animation: modalFadeIn 0.2s ease-out !important;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Focus States */
.wallet-adapter-modal-list .wallet-adapter-button:focus {
  outline: none !important;
  border-color: var(--accent-cyan) !important;
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2) !important;
}

/* Mobile Responsive */
@media (max-width: 640px) {
  .wallet-adapter-modal-container {
    width: 95% !important;
    max-width: none !important;
    margin: 1rem !important;
  }

  .wallet-adapter-modal-title {
    font-size: 1rem !important;
    padding: 1rem 1rem 0 1rem !important;
  }

  .wallet-adapter-modal-list {
    padding: 1rem !important;
  }

  .wallet-adapter-modal-list .wallet-adapter-button {
    min-height: 2.75rem !important;
    padding: 0.625rem 0.875rem !important;
    font-size: 0.8125rem !important;
  }

  .wallet-adapter-modal-button-close {
    right: 0.75rem !important;
    top: 0.75rem !important;
  }
}
