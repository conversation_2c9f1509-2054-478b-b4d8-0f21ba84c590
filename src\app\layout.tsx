import type { Metadata } from 'next'
import './globals.css'
import '@/lib/polyfills'
import { WalletProvider } from '@/components/providers/WalletProvider'
import { Header } from '@/components/layout/Header'

export const metadata: Metadata = {
  title: 'Solera Staking - Solana Meme Token Staking Platform',
  description: 'Stake your favorite Solana meme tokens and earn rewards',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className="font-solera min-h-screen">
        <WalletProvider>
          <div className="min-h-screen">
            <Header />
            <main className="px-6 pt-20 pb-8">
              {children}
            </main>
          </div>
        </WalletProvider>
      </body>
    </html>
  )
}
