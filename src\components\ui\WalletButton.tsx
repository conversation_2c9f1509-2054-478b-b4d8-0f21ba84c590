'use client'

import Link from 'next/link'
import { useWallet, useConnection } from '@solana/wallet-adapter-react'
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui'
import { useEffect, useState, useRef } from 'react'
import { PublicKey } from '@solana/web3.js'
import { getAssociatedTokenAddress, getAccount } from '@solana/spl-token'
import { User } from 'lucide-react'

// Solera RA token contract address
const SOLERA_RA_TOKEN_ADDRESS = '2jPF5RY4B3jtJb4iAwRZ5J68WLLu4uaaBZ4wpjV29YYA'

interface WalletButtonProps {
  onBalanceChange?: (balance: number | null, isLoading: boolean) => void
}

export function WalletButton({ onBalanceChange }: WalletButtonProps = {}) {
  const { connected, publicKey, disconnect } = useWallet()
  const { connection } = useConnection()
  const [tokenBalance, setTokenBalance] = useState<number | null>(null)
  const [isLoadingBalance, setIsLoadingBalance] = useState(false)
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Fetch Solera RA token balance
  useEffect(() => {
    const fetchTokenBalance = async () => {
      if (!connected || !publicKey) {
        setTokenBalance(null)
        onBalanceChange?.(null, false)
        return
      }

      setIsLoadingBalance(true)
      onBalanceChange?.(tokenBalance, true)
      try {
        const tokenMint = new PublicKey(SOLERA_RA_TOKEN_ADDRESS)
        const associatedTokenAddress = await getAssociatedTokenAddress(
          tokenMint,
          publicKey
        )

        const tokenAccount = await getAccount(connection, associatedTokenAddress)
        const balance = Number(tokenAccount.amount) / Math.pow(10, 6) // Assuming 6 decimals for RA token
        setTokenBalance(balance)
        onBalanceChange?.(balance, false)
      } catch (error) {
        console.error('Error fetching token balance:', error)
        setTokenBalance(0) // Default to 0 if account doesn't exist or error occurs
        onBalanceChange?.(0, false)
      } finally {
        setIsLoadingBalance(false)
      }
    }

    fetchTokenBalance()
  }, [connected, publicKey, connection, onBalanceChange, tokenBalance])

  // Click outside handler for dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsProfileDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Format token balance for display
  const formatBalance = (balance: number | null): string => {
    if (balance === null) return '0.00'
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(balance)
  }

  // Format wallet address for display
  const formatWalletAddress = (address: string): string => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`
  }

  // Handle wallet disconnect
  const handleDisconnect = async () => {
    try {
      await disconnect()
      setIsProfileDropdownOpen(false)
      setTokenBalance(null)
    } catch (error) {
      console.error('Error disconnecting wallet:', error)
    }
  }

  return (
    <div className="flex items-center space-x-3">
      {/* USD Currency - Hidden on mobile */}
      <span className="hidden sm:block text-xs text-gray-300 font-medium">USD</span>

      {connected ? (
        /* Connected State: Token Balance + Profile Avatar */
        <div className="flex items-center space-x-3">
          {/* Token Balance - Hidden on mobile */}
          <div className="hidden sm:block text-xs text-gray-300 font-medium">
            {isLoadingBalance ? (
              <span className="animate-pulse">Loading...</span>
            ) : (
              <span>{formatBalance(tokenBalance)} RA</span>
            )}
          </div>

          {/* Profile Avatar with Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setIsProfileDropdownOpen(!isProfileDropdownOpen)}
              className="w-7 h-7 flex items-center justify-center cursor-pointer transition-all
                md:bg-gradient-to-br md:from-cyan-500 md:to-blue-600 md:hover:scale-105 md:transition-transform
                bg-white hover:bg-gray-100 rounded md:rounded-full"
            >
              <User className="w-3.5 h-3.5 text-black md:text-white" />
            </button>

            {/* Profile Dropdown */}
            {isProfileDropdownOpen && (
              <div className="dropdown-menu absolute right-0 top-10 w-64 py-2 z-50">
                {/* Account Balance Section */}
                <div className="px-4 py-3 border-b" style={{ borderColor: 'var(--border-primary)' }}>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>Account Balance</span>
                    <button
                      onClick={handleDisconnect}
                      className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                    >
                      Disconnect
                    </button>
                  </div>
                  <div className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>$0.00</div>
                </div>

                {/* Solera Token Balance */}
                <div className="px-4 py-2 border-b" style={{ borderColor: 'var(--border-primary)' }}>
                  <div className="flex items-center justify-between">
                    <span className="text-sm" style={{ color: 'var(--text-primary)' }}>Solera</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm" style={{ color: 'var(--text-primary)' }}>RA {formatBalance(tokenBalance)}</span>
                      <div className="w-2 h-2 bg-blue-500"></div>
                    </div>
                  </div>
                </div>

                {/* Wallet Address */}
                {publicKey && (
                  <div className="px-4 py-2 border-b" style={{ borderColor: 'var(--border-primary)' }}>
                    <div className="text-xs mb-1" style={{ color: 'var(--text-secondary)' }}>Wallet</div>
                    <div className="text-sm font-mono" style={{ color: 'var(--text-primary)' }}>
                      {formatWalletAddress(publicKey.toString())}
                    </div>
                  </div>
                )}

                {/* Menu Items */}
                <div className="py-1">
                  <Link
                    href="/"
                    className="block px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                    style={{ color: 'var(--text-primary)' }}
                    onClick={() => setIsProfileDropdownOpen(false)}
                  >
                    Dashboard
                  </Link>
                  <Link
                    href="/deposits"
                    className="block px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                    style={{ color: 'var(--text-primary)' }}
                    onClick={() => setIsProfileDropdownOpen(false)}
                  >
                    Deposits
                  </Link>
                  <Link
                    href="/withdrawals"
                    className="block px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                    style={{ color: 'var(--text-primary)' }}
                    onClick={() => setIsProfileDropdownOpen(false)}
                  >
                    Withdrawals
                  </Link>
                  <Link
                    href="/history"
                    className="block px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                    style={{ color: 'var(--text-primary)' }}
                    onClick={() => setIsProfileDropdownOpen(false)}
                  >
                    Transaction History
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        /* Disconnected State: Connect Wallet Button */
        <WalletMultiButton>
          Connect Wallet
        </WalletMultiButton>
      )}
    </div>
  )
}
